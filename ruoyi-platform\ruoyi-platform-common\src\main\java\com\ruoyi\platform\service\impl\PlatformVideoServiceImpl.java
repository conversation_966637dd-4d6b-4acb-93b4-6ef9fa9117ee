package com.ruoyi.platform.service.impl;

import static com.ruoyi.platform.utils.taskUtils.PlatformVideoTaskStatus.*;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.lang.reflect.Method;
import java.net.URI;
import java.net.URL;
import java.net.URLConnection;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.ruoyi.common.annotation.DataScope;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.sign.Md5Utils;
import com.ruoyi.common.utils.spring.SpringUtils;
import com.ruoyi.common.utils.uuid.UUID;
import com.ruoyi.file.utils.FileOperateUtils;
import com.ruoyi.platform.domain.PlatformAudio;
import com.ruoyi.platform.domain.PlatformImage;
import com.ruoyi.platform.domain.PlatformModel;
import com.ruoyi.platform.domain.PlatformVideo;
import com.ruoyi.platform.domain.vo.DialogueSynthesisRequest;
import com.ruoyi.platform.mapper.PlatformAudioMapper;
import com.ruoyi.platform.mapper.PlatformImageMapper;
import com.ruoyi.platform.mapper.PlatformModelMapper;
import com.ruoyi.platform.mapper.PlatformVideoMapper;
import com.ruoyi.platform.service.IPlatformVideoService;
import com.ruoyi.platform.utils.taskUtils.FileUrlUtils;
import com.ruoyi.platform.utils.taskUtils.HttpClientUtil;
import com.ruoyi.platform.utils.taskUtils.MultipartFileUtils;
import com.ruoyi.platform.utils.taskUtils.PlatformVideoConfig;
import com.ruoyi.platform.utils.taskUtils.PlatformVideoTaskVersion;


/**
 * 视频合成Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-02-26
 */
@Service
public class PlatformVideoServiceImpl implements IPlatformVideoService
{
    private static final Logger log = LoggerFactory.getLogger(PlatformVideoServiceImpl.class);

    // 云剪辑相关常量
    private static final int MEDIA_READY_MAX_RETRIES = 6; // 媒资等待最大重试次数
    private static final int MEDIA_READY_RETRY_INTERVAL = 5000; // 媒资等待重试间隔(毫秒)
    private static final int DEFAULT_VIDEO_WIDTH = 1920; // 默认视频宽度
    private static final int DEFAULT_VIDEO_HEIGHT = 1080; // 默认视频高度
    private static final int DEFAULT_VIDEO_BITRATE = 2000000; // 默认视频码率(2Mbps)
    private static final int DEFAULT_VIDEO_FRAMERATE = 25; // 默认视频帧率
    private static final String DEFAULT_VIDEO_FORMAT = "mp4"; // 默认视频格式
    private static final String OSS_DIALOGUE_VIDEO_PATH = "dialogue_videos"; // 对话视频OSS路径

    // 预定义的模板配置
    private static final Map<String, String> DIALOGUE_TEMPLATES = new HashMap<String, String>() {{
        // 2人对话模板 - 支持3个媒体参数
        put("2_PERSON_DIALOGUE", "29bcaa98249349b79cdb693b21ba3860"); // 测试数据模板
        // 多人对话模板 - 支持3个媒体参数
        put("MULTI_PERSON_DIALOGUE", "a8ca479ecb554b9baefd8040f4592127"); // 默认模板
        // 备用模板
        put("FALLBACK_TEMPLATE", "29bcaa98249349b79cdb693b21ba3860"); // 测试数据模板
    }};

    @Autowired
    private PlatformVideoConfig platformVideoConfig;

    @Autowired
    private PlatformVideoMapper platformVideoMapper;

    @Autowired
    private PlatformModelMapper platformModelMapper;

    @Autowired
    private PlatformAudioMapper platformAudioMapper;

    @Autowired
    private PlatformImageMapper platformImageMapper;

    // 注入阿里云ICE客户端
    @Autowired
    private com.aliyun.ice20201109.Client iceClientAK;

    private static final String PARAM_MODEL = "model";
    private static final String PARAM_MESSAGES = "messages";
    private static final String PARAM_CONTENT = "content"; 

    //查询视频合成
    @Override
    public PlatformVideo selectPlatformVideoById(Long id){
        return platformVideoMapper.selectPlatformVideoById(id);
    }

    // 查询视频合成列表
    @Override
    @DataScope(deptAlias = "d", userAlias = "u")
    public List<PlatformVideo> selectPlatformVideoList(PlatformVideo platformVideo) {
        List<PlatformVideo> videoList = platformVideoMapper.selectPlatformVideoList(platformVideo);
        return videoList;
    }    

    //修改视频合成
    @Override
    public int updatePlatformVideo(PlatformVideo platformVideo){
        return platformVideoMapper.updatePlatformVideo(platformVideo);
    }

    //批量删除视频合成
    @Override
    public int deletePlatformVideoByIds(Long[] ids){
        for (Long id : ids) {
            PlatformVideo video = selectPlatformVideoById(id);
            if (video != null && StringUtils.isNotEmpty(video.getResultVideo())) {
                if ("2".equals(video.getStatus())) {
                    throw new ServiceException("任务正在处理中，不能删除！");  // 为处理中不让删除
                }
                try {
                    FileOperateUtils.deleteFile(video.getResultVideo()); // 只删除结果视频
                } catch (Exception e) {
                    log.error("删除文件失败", e);
                }
            }
        }
        return platformVideoMapper.deletePlatformVideoByIds(ids);
    }

    // 查询状态为可用的模型
    @Override
    public List<PlatformModel> getAvailableModels() {
        PlatformModel queryModel = new PlatformModel(); 
        queryModel.setModelStatus(1); //0 禁用 1=可用
        return platformModelMapper.selectWyModelList(queryModel);
    }

    //查询待处理和处理中的任务
    @Override
    public List<PlatformVideo> selectPendingTasks() {
        PlatformVideo queryTask = new PlatformVideo();
        queryTask.setStatus(STATUS_PENDING);  
        List<PlatformVideo> pendingTasks = platformVideoMapper.selectPlatformVideoList(queryTask);
        queryTask.setStatus(STATUS_PROCESSING);
        List<PlatformVideo> processingTasks = platformVideoMapper.selectPlatformVideoList(queryTask);
        pendingTasks.addAll(processingTasks);
        return pendingTasks;
    }


    //创建一条视频合成任务
    @Override
    public Long add(PlatformVideo platformVideo) {
        platformVideo.setNumber(FileUrlUtils.generateVideoName()); //任务编号
        platformVideo.setCreateBy(SecurityUtils.getUsername()); //创建人
        platformVideo.setUpdateBy(SecurityUtils.getUsername()); //修改人
        platformVideo.setCreatedAt(DateUtils.getNowDate()); //创建时间
        platformVideo.setUpdatedAt(DateUtils.getNowDate()); //修改时间
        platformVideo.setVersion(PlatformVideoTaskVersion.M_VERSION); // 设置版本号
        platformVideoMapper.insertPlatformVideo(platformVideo);
        Long taskId = platformVideo.getId();
        return taskId;
    }

    //查询视频合成单个任务
    @Override
    public PlatformVideo getOneTask(String version) {
        PlatformVideo condition = new PlatformVideo();
        condition.setStatus( "1"); // 1待处理 2处理中 3成功 4失败',
        condition.setVersion(version); // 版本号
        PlatformVideo musetalkTask = platformVideoMapper.selectPlatformVideoOne(condition);
        if (musetalkTask == null) {
            log.info("没有待处理M版合成任务");
            return null;
        }
        log.info("获取到待处理任务，任务编号为：{}", musetalkTask.getNumber());
        musetalkTask.setStatus("2"); // 改为 处理中
        platformVideoMapper.updatePlatformVideo(musetalkTask);
        return musetalkTask;
    }

    //根据版本查询视频合成单个任务
    @Override
    public PlatformVideo getOneTaskByVersion(String version) {
        PlatformVideo condition = new PlatformVideo();
        condition.setStatus( "1"); // 1待处理 2处理中 3成功 4失败',
        condition.setVersion(version); // 版本号
        PlatformVideo task = platformVideoMapper.selectPlatformVideoOne(condition);
        if (task == null) {
            log.info("没有待处理"+version+"版合成任务");
            return null;
        }
        log.info("获取到待处理任务，任务编号为：{}", task.getNumber());
        task.setStatus("2"); // 改为 处理中
        platformVideoMapper.updatePlatformVideo(task);
        return task;
    }

    //根据动态任务结果调整任务的状态
    @Override
    public void updateTaskStatus(Long id, Long status, String resultVideo) {
        PlatformVideo platformVideo = new PlatformVideo();
        platformVideo.setId(id);
        platformVideo.setStatus(String.valueOf(status));
        platformVideo.setResultVideo(resultVideo);
        if (status == 4) {
            JSONObject operation = new JSONObject(); // 处理失败时的错误信息 - 存入JSON
            operation.put("errorResult", "Task processing failed"); 
            platformVideo.setOperation(operation.toString());
        }
        if (status == 3) {
            platformVideo.setCompleteAt(DateUtils.getNowDate()); //设置视频合成的完成时间
        }
        platformVideoMapper.updatePlatformVideo(platformVideo);
    }

    //上传音频文件
    @Override
    public Object uploadAudioFile(MultipartFile file){
        try {
            if (file == null || file.isEmpty()) {
                throw new ServiceException("音频素材不能为空！");
            }
            long maxFileSize = 100 * 1024 * 1024; // 100MB
            if (file.getSize() > maxFileSize) {
                throw new ServiceException("音频素材文件不能超过100MB！");
            }
            String documentFileName = file.getOriginalFilename();
            String filePath = "video/audio"; // 音频素材文件存储位置
            long timestamp = System.currentTimeMillis();
            String uniqueFileName = timestamp + "_" + documentFileName;
            String fullPath = filePath + "/" + uniqueFileName;
            fullPath = FileOperateUtils.upload(fullPath, file, null);
            String md5 = Md5Utils.getMd5(file);
            Map<String, String> result = new HashMap<>();
            result.put("url", fullPath);
            result.put("md5", md5);
            JSONObject operation = new JSONObject();
            operation.put("driven_audio_md5", md5);
            result.put("operation", operation.toString());
            return result; 
        } catch (IOException e) {
            throw new ServiceException("上传音频文件失败: " + e.getMessage()); // 返回失败响应queryVideoTask
        }
    }

    //查询视频合成任务(Map参数版本) v版
    @SuppressWarnings("unchecked")
    public PlatformVideo queryVideoTask(String model, Long taskId, Map<String, Object> params) {
        if (params != null) {
            if (!params.containsKey("messages") || !params.containsKey("model")) {
                throw new ServiceException("请求参数不完整");
            }
            List<Map<String, Object>> messages = (List<Map<String, Object>>) params.get("messages");
            if (messages == null || messages.isEmpty()) {
                throw new ServiceException("messages不能为空");
            }
            Map<String, Object> content = (Map<String, Object>) messages.get(0).get("content");
            if (content == null || !content.containsKey("task_id")) {
                throw new ServiceException("task_id不能为空");
            }
            model = params.get("model").toString();
            taskId = Long.valueOf(content.get("task_id").toString());
        }
        PlatformVideo task = selectPlatformVideoById(taskId);
        if (task == null) {
            throw new ServiceException("任务不存在");
        }
        return task;
    }

    //上传媒体文件(视频或音频)
    @Override
    public Map<String, String> uploadMedia(MultipartFile file, String type) throws Exception {
        if (file == null || file.isEmpty()) {
            throw new ServiceException("文件不能为空");
        }
        if (file.getSize() > 500 * 1024 * 1024) {
            throw new ServiceException("文件不能超过500MB");
        }
        // 根据类型选择不同的存储基础路径
        String basePath;
        if ("video".equals(type)) {
            basePath = "video/result";
        } else if ("audio".equals(type)) {
            basePath = "video/audio";
        } else {
            basePath = "video/other";
        }
        String currentDate = new SimpleDateFormat("yyyy/MM/dd").format(new Date());

        // 使用时间戳的后6位作为前缀
        String timestamp = String.valueOf(System.currentTimeMillis());
        String shortTimestamp = timestamp.substring(Math.max(0, timestamp.length() - 6)); // 取后六位
        shortTimestamp = String.format("%06d", Long.parseLong(shortTimestamp)); // 补零保证6位
        String originalFilename = file.getOriginalFilename();
        String newFileName = shortTimestamp + "_" + originalFilename;
        String fullPath = String.format("%s/%s/%s", basePath, currentDate, newFileName);
        String savedPath = FileOperateUtils.upload(fullPath, file, null);
        // 构建结果
        Map<String, String> result = new HashMap<>();
        result.put("savedPath", fullPath);     // 真实存储路径，用于数据库存储
        result.put("url", savedPath);          // 临时访问URL，用于前端展示
        result.put("type", type);               // 文件类型
        return result;
    }

    //创建数字人视频合成任务 v版
    @SuppressWarnings("unchecked")
    @Override
    public Map<String, Object> createVideoSynthesisWithUrls(Map<String, Object> params) throws Exception {
        Map<String, Object> originalParams = JSON.parseObject(JSON.toJSONString(params));
        if (!params.containsKey(PARAM_MODEL)) {
            throw new ServiceException("缺少模型编码");
        }
        String modelCode = params.get(PARAM_MODEL).toString();
        PlatformModel modelInfo = platformModelMapper.selectWyModelByCode(modelCode);
        if (modelInfo == null || !modelInfo.getModelStatus().equals(1)) {
            throw new ServiceException("模型不可用");
        }
        List<Map<String, Object>> messages = (List<Map<String, Object>>) params.get(PARAM_MESSAGES);
        if (messages != null && !messages.isEmpty()) {
            Map<String, Object> content = (Map<String, Object>) messages.get(0).get(PARAM_CONTENT);
            if (content != null) {
                if (content.containsKey("live_video_url")) {
                    String videoPath = content.get("live_video_url").toString();
                    String originalPath = FileUrlUtils.extractStoragePath(videoPath);
                    if (StringUtils.isEmpty(originalPath)) {
                        throw new ServiceException("视频路径无效");
                    }
                    String tempUrl = FileOperateUtils.getURL(originalPath);
                    if (tempUrl != null) {
                        content.put("live_video_url", tempUrl);
                    }
                }
                if (content.containsKey("live_sound_url")) {
                    String audioPath = content.get("live_sound_url").toString();
                    String originalPath = FileUrlUtils.extractStoragePath(audioPath);
                    String tempUrl = FileOperateUtils.getURL(originalPath);
                    if (tempUrl != null) {
                        content.put("live_sound_url", tempUrl);
                    }
                }
            }
        }
        Map<String, Object> result = createVideoSynthesis(params);
        saveVideoTask(originalParams, modelCode, result);
        return result;
    }

    @SuppressWarnings("unchecked")
    private void saveVideoTask(Map<String, Object> params, String modelCode, Map<String, Object> result) {
        try {
            PlatformVideo task = new PlatformVideo();
            task.setModel(modelCode);
            task.setTaskNo(result.get("task_id").toString());
            task.setNumber(FileUrlUtils.generateVideoName());
            task.setVersion("V");
            task.setStatus(result.getOrDefault("status", STATUS_PENDING).toString());

            String username = SecurityUtils.getUsername();
            Date nowDate = DateUtils.getNowDate();
            task.setCreateBy(username);
            task.setUpdateBy(username);
            task.setCreatedAt(nowDate);
            task.setUpdatedAt(nowDate);

            if (params.containsKey(PARAM_MESSAGES)) {
                List<Map<String, Object>> messages = (List<Map<String, Object>>) params.get(PARAM_MESSAGES);
                if (!messages.isEmpty()) {
                    Map<String, Object> content = (Map<String, Object>) messages.get(0).get(PARAM_CONTENT);
                    if (content != null) {
                        if (content.containsKey("live_video_url")) {
                            String videoPath = content.get("live_video_url").toString();
                            String originalPath = FileUrlUtils.extractStoragePath(videoPath);
                            task.setDrivenVideo(originalPath);
                        }
                        if (content.containsKey("live_sound_url")) {
                            String audioPath = content.get("live_sound_url").toString();
                            String originalPath = FileUrlUtils.extractStoragePath(audioPath);
                            task.setDrivenAudio(originalPath);
                        }
                        task.setCallbackUrl(content.getOrDefault("callback_url", "").toString());
                    }
                }
            }
            PlatformModel modelInfo = platformModelMapper.selectWyModelByCode(modelCode);
            JSONObject jsonBuilder = new JSONObject();
            jsonBuilder.put("video_priority", "0");
            jsonBuilder.put("code", result.getOrDefault("code", "200"));
            jsonBuilder.put("video_message", result.getOrDefault("msg", "任务创建成功"));
            if (modelInfo != null && StringUtils.isNotEmpty(modelInfo.getModelVersion())) {
                jsonBuilder.put("model_price", modelInfo.getModelVersion());
            }
            String jsonStr = jsonBuilder.toJSONString();
            task.setOperation(jsonStr);
            int rows = platformVideoMapper.insertPlatformVideo(task);
            if (rows <= 0) {
                throw new ServiceException("保存任务记录失败");
            }
        } catch (Exception e) {
            throw new ServiceException("保存任务记录失败: " + e.getMessage());
        }
    }

    // 从临时凭证URL下载视频并重新上传到对象存储
    public static String downloadAndSaveVideo(String temporaryUrl) throws Exception {
        String tempFileName = UUID.randomUUID().toString() + ".mp4"; // 创建临时文件
        String tempFilePath = System.getProperty("java.io.tmpdir") + File.separator + tempFileName;
        File tempFile = new File(tempFilePath);
        try {
            URL url = new URI(temporaryUrl).toURL();
            URLConnection conn = url.openConnection();
            conn.setConnectTimeout(30000);  // 设置30秒连接超时
            conn.setReadTimeout(120000);    // 设置120秒读取超时
            try (InputStream inputStream = conn.getInputStream();
                 FileOutputStream outputStream = new FileOutputStream(tempFile)) {
                byte[] buffer = new byte[8192];
                int bytesRead;
                while ((bytesRead = inputStream.read(buffer)) != -1) {
                    outputStream.write(buffer, 0, bytesRead);
                }
            }
            String currentDate = new SimpleDateFormat("yyyy/MM/dd").format(new Date());
            String timestamp = String.valueOf(System.currentTimeMillis());
            String fileName = timestamp + ".mp4";
            String address = "video/result";
            String fullPath = String.format("%s/%s/%s", address, currentDate, fileName);
            MultipartFile multipartFile = MultipartFileUtils.createFromFile(
                tempFile, "file", fileName, "video/mp4");
            String savedPath = FileOperateUtils.upload(fullPath, multipartFile);
            return savedPath;
        } catch (Exception e) {
            throw e;
        } finally {
            if (tempFile.exists()) {
                if (!tempFile.delete()) {
                    log.warn("临时文件删除失败: {}", tempFilePath); // 清理临时文件
                }
            }
        }
    }

    @Override
    @SuppressWarnings("unchecked")
    public Map<String, Object> createVideoSynthesis(Map<String, Object> params) {
        if (platformVideoConfig == null || StringUtils.isEmpty(platformVideoConfig.getServerUrl())) {
            throw new RuntimeException("视频合成配置不完整");  // 配置检查
        }
        String modelCode = String.valueOf(params.get(PARAM_MODEL));
        PlatformModel modelInfo = platformModelMapper.selectWyModelByCode(modelCode); // 获取并记录模型价位信息
        String url = platformVideoConfig.getServerUrl().trim();
        if (!url.endsWith("/")) {
            url += "/";  // 构建完整URL - 确保正确拼接
        }
        url += platformVideoConfig.getSynthesisPath().trim().replaceFirst("^/", "");
        Map<String, String> headers = new HashMap<>();
        headers.put("Authorization", "Bearer " + platformVideoConfig.getApiKey());
        headers.put("Content-Type", "application/json");
        headers.put("Accept", "application/json");
        try {
            String jsonBody = JSON.toJSONString(params);
            String response = HttpClientUtil.doPost(url, jsonBody, headers);
            Map<String, Object> apiResponse = JSON.parseObject(response);
            Map<String, Object> result = new HashMap<>();
            if (apiResponse != null) {
                result.put("code", apiResponse.getOrDefault("code", 500));
                result.put("msg", apiResponse.getOrDefault("msg", "未知错误"));
                Object dataObj = apiResponse.get("data"); // 获取data
                if (dataObj instanceof Map) {
                    Map<String, Object> data = (Map<String, Object>) dataObj;
                    Object taskId = data.get("task_id");  // 获取task_id
                    result.put("task_id", taskId != null ? taskId.toString() : "");
                    result.put("status", STATUS_PENDING);
                    // 添加模型价位信息到结果中
                    if (modelInfo != null && StringUtils.isNotEmpty(modelInfo.getModelVersion())) {
                        result.put("model_price", modelInfo.getModelVersion());
                    }
                } else {
                    result.put("task_id", "");
                    result.put("status", STATUS_FAILED);
                }
            } else {
                result.put("code", 500);
                result.put("msg", "API响应为空");
                result.put("task_id", "");
                result.put("status", STATUS_FAILED);
            }
            return result;
        } catch (Exception e) {
            throw new ServiceException("创建视频合成任务失败: " + e.getMessage());
        }
    }

    @Override
    @SuppressWarnings("unchecked")
    public Map<String, Object> queryVideoSynthesis(String taskNo) {
        if (StringUtils.isEmpty(platformVideoConfig.getQueryPath())) {
            throw new ServiceException("查询接口路径未配置");
        }
        String url = platformVideoConfig.getServerUrl() + platformVideoConfig.getQueryPath();
        try {
            Map<String, Object> params = new HashMap<>();  // 构造查询参数
            params.put("model", "umi_video_v5");

            Map<String, Object> content = new HashMap<>();
            content.put("type", 2);
            content.put("task_id", taskNo);

            List<Map<String, Object>> messages = new ArrayList<>();
            Map<String, Object> message = new HashMap<>();
            message.put("role", "user");
            message.put("content", content);
            messages.add(message);
            params.put("messages", messages);

            String jsonBody = JSON.toJSONString(params);
            Map<String, String> headers = new HashMap<>();
            headers.put("Authorization", "Bearer " + platformVideoConfig.getApiKey());
            headers.put("Content-Type", "application/json");
            headers.put("Accept", "application/json");
            String response = HttpClientUtil.doPost(url, jsonBody, headers);  // 发送POST请求
            Map<String, Object> apiResponse = JSON.parseObject(response);
            Map<String, Object> result = new HashMap<>();  // 构造标准响应
            result.put("taskNo", taskNo);
            result.put("code", apiResponse.getOrDefault("code", 500));
            result.put("message", apiResponse.getOrDefault("msg", "未知错误"));
            
            Map<String, Object> data = (Map<String, Object>) apiResponse.get("data"); // 处理data部分
            if (data != null) {
                Integer status = (Integer) data.get("status");
                PlatformVideo queryTask = new PlatformVideo(); // 查询任务信息
                queryTask.setTaskNo(taskNo);
                List<PlatformVideo> tasks = selectPlatformVideoList(queryTask);
                PlatformVideo task = !tasks.isEmpty() ? tasks.get(0) : null;
                
                // 如果任务存在，添加模型价位信息到结果中
                if (task != null && StringUtils.isNotEmpty(task.getOperation())) {
                    try {
                        JSONObject operationJson = JSONObject.parseObject(task.getOperation());
                        if (operationJson.containsKey("model_price")) {
                            String modelPrice = operationJson.getString("model_price");
                            result.put("modelPrice", modelPrice);
                        } else {
                            log.warn("任务[{}]的操作JSON中未找到model_price字段", task.getId());
                        }
                    } catch (Exception e) {
                        log.warn("解析操作JSON失败: {}", e.getMessage());
                    }
                }
                switch (status) {
                    case 1:
                        result.put("status", STATUS_PENDING);
                        result.put("message", STATUS_DESC_PENDING);
                        break;
                    case 2:
                        result.put("status", STATUS_PROCESSING);
                        result.put("message", STATUS_DESC_PROCESSING);
                        break;
                    case 3: // 成功状态
                        String videoUrl = data.get("complete_url").toString();
                        try {
                            if (task != null && StringUtils.isEmpty(task.getResultVideo())) {
                                // 下载并保存视频，返回的是相对路径
                                String savedPath = downloadAndSaveVideo(videoUrl);
                                
                                result.put("status", STATUS_SUCCESS);
                                result.put("message", STATUS_DESC_SUCCESS);
                                result.put("originalStoragePath", savedPath);
                                
                                PlatformVideo updateTask = new PlatformVideo();
                                updateTask.setId(task.getId());
                                updateTask.setStatus(STATUS_SUCCESS);
                                updateTask.setVideoMessage(STATUS_DESC_SUCCESS);
                                // 关键修改：确保存储的是相对路径，不是临时URL
                                updateTask.setResultVideo(savedPath); // 这里必须是相对路径
                                Date nowDate = new Date();
                                updateTask.setCompleteAt(nowDate);
                                updateTask.setUpdatedAt(nowDate);
                                updateTask.setUpdateBy(SecurityUtils.getUsername());
                                platformVideoMapper.updatePlatformVideo(updateTask);
                                
                                // 生成临时访问URL仅供前端显示使用
                                String tempUrl = FileOperateUtils.getURL(savedPath);
                                result.put("videoUrl", tempUrl);
                                result.put("realVideoUrl", savedPath); // 真实的相对路径
                            } else if (task != null && StringUtils.isNotEmpty(task.getResultVideo())) {
                                // 任务已处理过，直接生成临时访问URL
                                result.put("status", STATUS_SUCCESS);
                                result.put("message", STATUS_DESC_SUCCESS);
                                result.put("originalStoragePath", task.getResultVideo());
                                
                                // 生成临时访问URL供前端显示
                                String tempUrl = FileOperateUtils.getURL(task.getResultVideo());
                                result.put("videoUrl", tempUrl);
                            }
                        } catch (Exception e) {
                            log.error("处理视频URL失败");
                        }
                        break;
                    case 4:
                        result.put("status", STATUS_FAILED);
                        result.put("message", STATUS_DESC_FAILED);
                        if (task != null) {
                            task.setStatus(STATUS_FAILED);
                            task.setVideoMessage(STATUS_DESC_FAILED);
                            task.setUpdateTime(new Date());
                            task.setUpdateBy(SecurityUtils.getUsername());
                            platformVideoMapper.updatePlatformVideo(task);
                        } else {
                            log.warn("无法找到任务ID为 {} 的记录", taskNo);
                        }
                        break;
                    default:
                        throw new ServiceException("未知状态码: " + status);
                }
                if (task != null) {
                    result.put("taskId", task.getId()); // 添加其他任务信息
                    result.put("model", task.getModel());
                    result.put("videoName", task.getNumber());
                    // 处理源文件URL
                    if (StringUtils.isNotEmpty(task.getDrivenVideo())) {
                        String videoTempUrl = FileOperateUtils.getURL(task.getDrivenVideo());
                        result.put("liveVideoUrl", videoTempUrl.toString());
                    }
                    if (StringUtils.isNotEmpty(task.getDrivenAudio())) {
                        String audioTempUrl = FileOperateUtils.getURL(task.getDrivenAudio());
                        result.put("liveSoundUrl", audioTempUrl.toString());
                    }
                    result.put("createTime", task.getCreateTime());
                    result.put("updateTime", task.getUpdateTime());
                }
            }
            return result;
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }
    }

    /**
     * 创建任务  合成 H 版视频
     */
    @Override
    public Long synthesisH(PlatformVideo platformVideo) {
        platformVideo.setNumber(FileUrlUtils.generateVideoName()); //任务编号
        platformVideo.setCreateBy(SecurityUtils.getUsername()); //创建人
        platformVideo.setUpdateBy(SecurityUtils.getUsername()); //修改人
        platformVideo.setCreatedAt(DateUtils.getNowDate()); //创建时间
        platformVideo.setUpdatedAt(DateUtils.getNowDate()); //修改时间
        platformVideo.setVersion(PlatformVideoTaskVersion.H_VERSION); // 设置版本号
        platformVideoMapper.insertPlatformVideo(platformVideo);
        Long taskId = platformVideo.getId();
        return taskId;

    }

    /**
     * 数字人对话合成
     */
    @Override
    public Map<String, Object> createDialogueSynthesis(DialogueSynthesisRequest request) {
        validateDialogueRequest(request);

        // 生成对话组ID
        String dialogueGroupId = "dialogue_" + System.currentTimeMillis();

        List<Long> videoTaskIds = request.getDialogueContent().stream().map(dialogue -> {
            // 查找对应的数字人配置
            DialogueSynthesisRequest.DigitalHuman human = request.getDigitalHumans().stream()
                .filter(h -> dialogue.getSpeaker().equals(h.getId())).findFirst()
                .orElseThrow(() -> new ServiceException("找不到发言人配置: " + dialogue.getSpeakerName()));
            Map<String, String> audioResult = generateAudioForText(dialogue.getText(), human); // 生成音频

            // 创建视频任务，并添加对话组ID和顺序信息
            return createVideoTask(audioResult.get("audioUrl"), audioResult.get("audioMd5"),
                human, dialogue, request.getVersion(), request.getBboxShiftValue(), request.getModel(), dialogueGroupId);
        }).collect(Collectors.toList());

        // 返回结果包含任务ID列表和对话组ID
        Map<String, Object> result = new HashMap<>();
        result.put("videoTaskIds", videoTaskIds);
        result.put("dialogueGroupId", dialogueGroupId);
        result.put("message", "数字人对话合成任务已创建");

        return result;
    }

    /**
     * 验证对话合成请求参数
     */
    private void validateDialogueRequest(DialogueSynthesisRequest request) {
        if (request.getDigitalHumans() == null || request.getDigitalHumans().isEmpty()) {
            throw new ServiceException("数字人配置不能为空");
        }
        if (request.getDialogueContent() == null || request.getDialogueContent().isEmpty()) {
            throw new ServiceException("对话内容不能为空");
        }
        String version = request.getVersion();
        if (!"M".equalsIgnoreCase(version) && !"H".equalsIgnoreCase(version) && !"V".equalsIgnoreCase(version)) {
            throw new ServiceException("不支持的版本类型: " + version);
        }
        // 数字人配置验证
        String[] supportedVoices = {"zhiyuan", "zhiyue", "zhisha", "zhida", "aiqi", "aicheng", "aijia","siqi", "sijia", "mashu", "yueer", "ruoxi", "aida", "sicheng","ninger", "xiaoyun", "xiaogang", "ruilin"};
        for (DialogueSynthesisRequest.DigitalHuman human : request.getDigitalHumans()) {
            if (StringUtils.isEmpty(human.getAvatarAddress()) || StringUtils.isEmpty(human.getVoiceName())) {
                throw new ServiceException("数字人配置不完整");
            }
            if ("system".equals(human.getVoiceType()) && human.getVoiceId() == null) {
                throw new ServiceException("系统声音ID不能为空");
            }
            if ("builtin".equals(human.getVoiceType())) {
                boolean isSupported = false;
                for (String voice : supportedVoices) {
                    if (voice.equals(human.getVoiceName())) {
                        isSupported = true;
                        break;
                    }
                }
                if (!isSupported) {
                    throw new ServiceException("不支持的内置音色: " + human.getVoiceName());
                }
            }
        }
        if ("M".equalsIgnoreCase(version) && request.getBboxShiftValue() != null) {
            int bboxValue = request.getBboxShiftValue();
            if (bboxValue < -7 || bboxValue > 7) {
                throw new ServiceException("M版边界框偏移值必须在-7到+7之间");
            }
        }
        if ("V".equalsIgnoreCase(version)) {
            if (StringUtils.isEmpty(request.getModel())) {
                throw new ServiceException("V版必须指定模型编码");
            }
            List<PlatformModel> models = getAvailableModels();
            boolean modelExists = models.stream().anyMatch(model -> request.getModel().equals(model.getModelCode()));
            if (!modelExists) {
                throw new ServiceException("模型不存在或不可用: " + request.getModel());
            }
        }
    }

    /**
     * 为单个文本生成音频
     */
    private Map<String, String> generateAudioForText(String text, DialogueSynthesisRequest.DigitalHuman human) {
        try {
            return switch (human.getVoiceType()) {
                case "system" -> generateSystemAudio(text, human.getVoiceId());
                case "builtin" -> generateBuiltinAudio(text, human.getVoiceName());
                default -> throw new ServiceException("不支持的声音类型: " + human.getVoiceType());
            };
        } catch (Exception e) {
            log.error("音频生成失败 - 文本: {}, 声音类型: {}, 错误: {}",
                text.substring(0, Math.min(20, text.length())), human.getVoiceType(), e.getMessage());
            throw new ServiceException("音频生成失败: " + e.getMessage());
        }
    }

    /**
     * 创建视频合成任务
     */
    private Long createVideoTask(String audioUrl, String audioMd5, DialogueSynthesisRequest.DigitalHuman human,
    DialogueSynthesisRequest.DialogueContent dialogue, String version, Integer bboxShiftValue, String modelCode, String dialogueGroupId) {
        if ("V".equalsIgnoreCase(version)) {
            return createVVersionTask(audioUrl, audioMd5, human, dialogue, modelCode, dialogueGroupId);
        }
        PlatformVideo videoTask = buildVideoTask(audioUrl, human, dialogue, audioMd5, version, bboxShiftValue, dialogueGroupId);
        return "M".equalsIgnoreCase(version) ? add(videoTask) : synthesisH(videoTask);
    }

    /**
     * 构建视频任务对象
     */
    private PlatformVideo buildVideoTask(String audioUrl, DialogueSynthesisRequest.DigitalHuman human,
        DialogueSynthesisRequest.DialogueContent dialogue, String audioMd5, String version, Integer bboxShiftValue, String dialogueGroupId) {
        PlatformVideo videoTask = new PlatformVideo();
        videoTask.setDrivenAudio(audioUrl);
        videoTask.setDrivenVideo(human.getAvatarAddress());
        videoTask.setStatus("1");
        JSONObject operation = buildBaseOperationJson(human, dialogue, audioMd5, version);
        if ("M".equalsIgnoreCase(version) && bboxShiftValue != null) {
            operation.put("bbox_shift_value", bboxShiftValue);
        }
        // 添加对话组ID和顺序信息
        if (StringUtils.isNotEmpty(dialogueGroupId)) {
            operation.put("dialogueGroupId", dialogueGroupId);
            operation.put("dialogueOrder", dialogue.getOrder() != null ? dialogue.getOrder() : 0);
        }
        videoTask.setOperationJson(operation);
        return videoTask;
    }

    /**
     * 创建V版视频合成任务
     */
    private Long createVVersionTask(String audioUrl, String audioMd5, DialogueSynthesisRequest.DigitalHuman human,
        DialogueSynthesisRequest.DialogueContent dialogue, String modelCode, String dialogueGroupId) {
        try {
            String videoName = FileUrlUtils.generateVideoName();
            Map<String, Object> params = new HashMap<>();
            params.put(PARAM_MODEL, modelCode);
            Map<String, Object> content = new HashMap<>();
            content.put("type", 1);
            content.put("video_name", videoName);
            try {
                String audioTempUrl = FileOperateUtils.getURL(audioUrl);
                String videoTempUrl = FileOperateUtils.getURL(human.getAvatarAddress());
                content.put("live_sound_url", audioTempUrl != null ? audioTempUrl : audioUrl);
                content.put("live_video_url", videoTempUrl != null ? videoTempUrl : human.getAvatarAddress());
            } catch (Exception e) {
                log.warn("获取临时URL失败，使用原始URL");
                content.put("live_sound_url", audioUrl);
                content.put("live_video_url", human.getAvatarAddress());
            }
            Map<String, Object> message = new HashMap<>();
            message.put("role", "user");
            message.put("content", content);
            List<Map<String, Object>> messages = new ArrayList<>();
            messages.add(message);
            params.put("messages", messages);
            Map<String, Object> apiResult = createVideoSynthesis(params);// 调用V版创建任务接口
            if (apiResult.containsKey("code")) {
                Object code = apiResult.get("code");
                if (!"200".equals(String.valueOf(code)) && !Integer.valueOf(200).equals(code)) {
                    log.error("V版API调用失败 - code: {}, msg: {}", code, apiResult.get("msg"));
                }
            }
            return saveVVersionTask(modelCode, apiResult, audioUrl, audioMd5, human, dialogue, videoName, dialogueGroupId);
        } catch (Exception e) {
            throw new ServiceException("创建V版视频任务失败: " + e.getMessage());
        }
    }

    /**
     * 保存V版任务到数据库
     */
    private Long saveVVersionTask(String modelCode, Map<String, Object> apiResult, String audioUrl, String audioMd5,
        DialogueSynthesisRequest.DigitalHuman human, DialogueSynthesisRequest.DialogueContent dialogue, String videoName, String dialogueGroupId) {
        try {
            String taskId = "";
            if (apiResult.containsKey("task_id")) {
                taskId = apiResult.get("task_id").toString();
            } else if (apiResult.containsKey("data") && apiResult.get("data") instanceof Map) {
                @SuppressWarnings("unchecked")
                Map<String, Object> data = (Map<String, Object>) apiResult.get("data");
                if (data.containsKey("task_id")) {
                    taskId = data.get("task_id").toString();
                }
            }
            PlatformVideo task = new PlatformVideo();
            task.setModel(modelCode);
            task.setTaskNo(taskId);
            task.setNumber(videoName);
            task.setVersion(PlatformVideoTaskVersion.V_VERSION);
            task.setStatus(apiResult.getOrDefault("status", STATUS_PENDING).toString());
            task.setDrivenAudio(audioUrl);
            task.setDrivenVideo(human.getAvatarAddress());
            task.setCreateBy(SecurityUtils.getUsername());
            task.setUpdateBy(SecurityUtils.getUsername());
            task.setCreatedAt(DateUtils.getNowDate());
            task.setUpdatedAt(DateUtils.getNowDate());
            JSONObject operation = buildBaseOperationJson(human, dialogue, audioMd5, "V");
            operation.put("video_priority", "0");
            operation.put("code", apiResult.getOrDefault("code", "200"));
            operation.put("video_message", apiResult.getOrDefault("msg", "任务创建成功"));
            // 添加对话组ID和顺序信息
            if (StringUtils.isNotEmpty(dialogueGroupId)) {
                operation.put("dialogueGroupId", dialogueGroupId);
                operation.put("dialogueOrder", dialogue.getOrder() != null ? dialogue.getOrder() : 0);
            }
            PlatformModel modelInfo = platformModelMapper.selectWyModelByCode(modelCode);
            if (modelInfo != null && StringUtils.isNotEmpty(modelInfo.getModelVersion())) {
                operation.put("model_price", modelInfo.getModelVersion());
            }
            task.setOperation(operation.toJSONString());
            int rows = platformVideoMapper.insertPlatformVideo(task);
            if (rows <= 0) {
                throw new ServiceException("保存V版任务记录失败");
            }
            return task.getId();
        } catch (Exception e) {
            throw new ServiceException("保存V版任务记录失败: " + e.getMessage());
        }
    }

    /**
     * 使用数智宝系统声音生成音频
     */
    private Map<String, String> generateSystemAudio(String text, Long voiceId) {
        try {
            Object platformTaskService = SpringUtils.getBean("platformTaskServiceImpl");
            if (platformTaskService == null) {
                throw new ServiceException("无法获取PlatformTaskService服务");
            }
            // 调用同步方法，返回音频路径
            Method syncMethod = platformTaskService.getClass().getMethod("createTextToAudioSync", String.class, Long.class, Long.class);
            String audioPath = (String) syncMethod.invoke(platformTaskService, text, voiceId, null);

            if (StringUtils.isEmpty(audioPath)) {
                throw new ServiceException("系统音频合成失败");
            }
            String audioMd5 = FileOperateUtils.getMd5ForFilePath(audioPath);
            if (StringUtils.isEmpty(audioMd5)) {
                PlatformAudio audio = platformAudioMapper.getAudioDetailByAddress(audioPath);
                if (audio != null) {
                    audioMd5 = audio.getAudioMd5();
                }
            }
            Map<String, String> result = new HashMap<>();
            result.put("audioUrl", audioPath);
            result.put("audioMd5", audioMd5);
            return result;
        } catch (Exception e) {
            throw new ServiceException("系统音频合成失败: " + e.getMessage());
        }
    }

    /**
     * 使用内置音色生成音频（阿里云）
     */
    private Map<String, String> generateBuiltinAudio(String text, String voiceName) {
        try {
            Object platformTaskService = SpringUtils.getBean("platformTaskServiceImpl");
            Object ttsRequest = createTtsRequest(text, voiceName);
            Method method = platformTaskService.getClass().getMethod("synthesize", ttsRequest.getClass());
            String audioPath = (String) method.invoke(platformTaskService, ttsRequest);
            if (StringUtils.isEmpty(audioPath)) {
                throw new ServiceException("内置音色合成失败");
            }
            String audioMd5 = FileOperateUtils.getMd5ForFilePath(audioPath);
            if (StringUtils.isEmpty(audioMd5)) {
                PlatformAudio audio = platformAudioMapper.getAudioDetailByAddress(audioPath);
                if (audio != null) {
                    audioMd5 = audio.getAudioMd5();
                }
            }
            Map<String, String> result = new HashMap<>();
            result.put("audioUrl", audioPath);
            result.put("audioMd5", audioMd5);
            return result;
        } catch (Exception e) {
            throw new ServiceException("内置音色合成失败: " + e.getMessage());
        }
    }

    /**
     * 创建TtsRequest对象
     */
    private Object createTtsRequest(String text, String voiceName) {
        try {
            Class<?> ttsRequestClass = Class.forName("com.ruoyi.platform.model.domain.TtsRequest");
            Object ttsRequest = ttsRequestClass.getDeclaredConstructor().newInstance();
            setTtsProperty(ttsRequest, "setText", text);
            setTtsProperty(ttsRequest, "setVoice", voiceName != null ? voiceName : "zhiyuan");
            setTtsProperty(ttsRequest, "setFormat", "wav");
            setTtsProperty(ttsRequest, "setSampleRate", 16000);
            setTtsProperty(ttsRequest, "setVolume", 50);
            return ttsRequest;
        } catch (Exception e) {
            throw new ServiceException("创建TtsRequest对象失败: " + e.getMessage());
        }
    }

    /**
     * 设置TTS对象属性
     */
    private void setTtsProperty(Object ttsRequest, String methodName, Object value) throws Exception {
        Class<?> paramType = value instanceof Integer ? int.class : String.class;
        java.lang.reflect.Method method = ttsRequest.getClass().getMethod(methodName, paramType);
        method.invoke(ttsRequest, value);
    }

    /**
     * 构建基础操作信息JSON
     */
    private JSONObject buildBaseOperationJson(DialogueSynthesisRequest.DigitalHuman human,
        DialogueSynthesisRequest.DialogueContent dialogue, String audioMd5, String version) {
        JSONObject operation = new JSONObject();
        operation.put("dialogue_id", dialogue.getId());
        operation.put("speaker_id", dialogue.getSpeaker());
        operation.put("speaker_name", dialogue.getSpeakerName());
        operation.put("text", dialogue.getText());
        operation.put("avatar_name", human.getAvatarName());
        operation.put("voice_name", human.getVoiceName());
        operation.put("voice_type", human.getVoiceType());
        operation.put("task_type", "dialogue_synthesis_item");
        operation.put("driven_audio_md5", audioMd5);
        operation.put("driven_video_md5", getAvatarMd5(human.getAvatarAddress()));
        operation.put("model_price", "V".equalsIgnoreCase(version) ? "600" : "500");
        return operation;
    }

    /**
     * 获取形象MD5值 - 直接查询数据库
     */
    private String getAvatarMd5(String avatarAddress) {
        if (StringUtils.isEmpty(avatarAddress)) {
            return null;
        }
        try {
            Map<String, Object> condition = Map.of("imageAddress", avatarAddress);
            List<PlatformImage> images = platformImageMapper.selectImagesByCondition(condition);
            return images != null && !images.isEmpty() ? images.get(0).getMd5() : null;
        } catch (Exception e) {
            return null;
        }
    }



    /**
     * 构建视频片段列表
     *
     * 将数字人视频任务转换为云剪辑所需的视频片段格式。
     * 每个片段包含视频URL（优先使用MediaId）、时长等信息。
     *
     * @param videoTasks 已完成的数字人视频任务列表
     * @return 视频片段列表，每个片段包含videoUrl、duration、order等信息
     */
    private List<Map<String, Object>> buildVideoClips(List<PlatformVideo> videoTasks) {
        List<Map<String, Object>> clips = new ArrayList<>();

        for (int i = 0; i < videoTasks.size(); i++) {
            PlatformVideo task = videoTasks.get(i);
            String videoUrl;
            try {
                // 方案1：尝试注册为ICE媒资并获取MediaId
                String mediaId = registerOssFileAsMedia(task.getResultVideo());
                if (StringUtils.isNotEmpty(mediaId)) {
                    videoUrl = mediaId; // 使用MediaId而不是URL
                    log.info("为任务ID {} 注册ICE媒资成功，MediaId: {}", task.getId(), mediaId);
                } else {
                    // 方案2：回退到标准OSS URL
                    videoUrl = convertToStandardOssUrl(task.getResultVideo());
                    log.info("为任务ID {} 使用标准OSS URL: {}", task.getId(), videoUrl);
                }
            } catch (Exception e) {
                log.warn("注册ICE媒资失败，回退到标准OSS URL，任务ID: {}", task.getId());
                try {
                    videoUrl = convertToStandardOssUrl(task.getResultVideo());
                } catch (Exception ex) {
                    throw new ServiceException("无法处理视频URL，任务ID: " + task.getId() + "，错误: " + ex.getMessage());
                }
            }

            if (StringUtils.isEmpty(videoUrl)) {
                throw new ServiceException("无法获取视频URL，任务ID: " + task.getId());
            }

            Map<String, Object> clip = new HashMap<>();
            clip.put("taskId", task.getId());
            clip.put("videoUrl", videoUrl);
            clip.put("order", i + 1);
            clip.put("duration", 10.0); // 简化处理，假设每个视频10秒

            // 从任务的operationJson中提取发言人和文本信息
            if (StringUtils.isNotEmpty(task.getOperation())) {
                JSONObject operation = JSONObject.parseObject(task.getOperation());
                if (operation.containsKey("speakerName")) {
                    clip.put("speakerName", operation.getString("speakerName"));
                }
                if (operation.containsKey("text")) {
                    clip.put("text", operation.getString("text"));
                }
            }

            clips.add(clip);
        }

        return clips;
    }

    /**
     * 转换为阿里云ICE要求的标准OSS URL格式（不带签名）
     */
    private String convertToStandardOssUrl(String videoPath) {
        // 如果是相对路径，转换为完整的OSS HTTPS URL
        if (videoPath.startsWith("video/")) {
            return "https://szb-pc.oss-cn-beijing.aliyuncs.com/" + videoPath;
        }

        // 如果已经是完整的HTTPS URL，移除签名参数
        if (videoPath.startsWith("https://") || videoPath.startsWith("http://")) {
            if (videoPath.contains("?")) {
                videoPath = videoPath.substring(0, videoPath.indexOf("?"));
            }
            // 确保使用HTTPS协议
            if (videoPath.startsWith("http://")) {
                videoPath = videoPath.replace("http://", "https://");
            }
            return videoPath;
        }

        throw new ServiceException("不支持的视频路径格式: " + videoPath);
    }

    /**
     * 将OSS文件注册为阿里云ICE媒资
     */
    private String registerOssFileAsMedia(String videoPath) {
        try {
            // 转换为标准OSS URL
            String ossUrl = convertToStandardOssUrl(videoPath);

            // 创建注册媒资请求
            com.aliyun.ice20201109.models.RegisterMediaInfoRequest request =
                new com.aliyun.ice20201109.models.RegisterMediaInfoRequest();

            request.setInputURL(ossUrl);
            request.setMediaType("video");
            request.setBusinessType("media");

            // 调用阿里云ICE API注册媒资
            com.aliyun.ice20201109.models.RegisterMediaInfoResponse response =
                iceClientAK.registerMediaInfo(request);

            if (response != null && response.getBody() != null) {
                String mediaId = response.getBody().getMediaId();
                log.info("OSS文件注册为ICE媒资成功，URL: {}, MediaId: {}", ossUrl, mediaId);

                // 等待媒资分析完成
                if (waitForMediaReady(mediaId)) {
                    log.info("媒资分析完成，MediaId: {}", mediaId);
                    return mediaId;
                } else {
                    log.warn("媒资分析超时，MediaId: {}", mediaId);
                    return null; // 返回null，回退到使用URL
                }
            }

        } catch (Exception e) {
            // 检查是否是重复注册错误
            if (e.getMessage().contains("has already been registered with mediaId")) {
                // 从错误消息中提取已存在的MediaId
                String existingMediaId = extractMediaIdFromErrorMessage(e.getMessage());
                if (StringUtils.isNotEmpty(existingMediaId)) {
                    log.info("OSS文件已注册为ICE媒资，使用现有MediaId: {}", existingMediaId);

                    // 即使是已存在的媒资，也要确保它已经就绪
                    if (waitForMediaReady(existingMediaId)) {
                        log.info("已存在媒资确认就绪，MediaId: {}", existingMediaId);
                        return existingMediaId;
                    } else {
                        log.warn("已存在媒资未就绪，MediaId: {}", existingMediaId);
                        return null; // 返回null，回退到使用URL
                    }
                }
            }
            log.warn("注册OSS文件为ICE媒资失败: {}", e.getMessage());
        }

        return null;
    }

    /**
     * 从错误消息中提取已存在的MediaId
     */
    private String extractMediaIdFromErrorMessage(String errorMessage) {
        try {
            // 错误消息格式: "has already been registered with mediaId \"ef0a7cd06d1871f0af90e7f7d45b6302\""
            String pattern = "mediaId \"([a-f0-9]{32})\"";
            java.util.regex.Pattern regex = java.util.regex.Pattern.compile(pattern);
            java.util.regex.Matcher matcher = regex.matcher(errorMessage);
            if (matcher.find()) {
                return matcher.group(1);
            }
        } catch (Exception e) {
            log.warn("提取MediaId失败: {}", e.getMessage());
        }
        return null;
    }



    /**
     * 构建云剪辑Timeline
     */
    private String buildVideoClipTimeline(List<Map<String, Object>> videoClips) {
        JSONObject timeline = new JSONObject();

        // 创建视频轨道
        JSONObject videoTrack = new JSONObject();
        List<JSONObject> videoTrackClips = new ArrayList<>();

        double currentTime = 0.0;

        for (Map<String, Object> clip : videoClips) {
            JSONObject videoClip = new JSONObject();
            String videoUrl = clip.get("videoUrl").toString();

            // 判断是MediaId还是URL - 修复判断逻辑
            if (videoUrl.length() == 32 && !videoUrl.startsWith("http")) {
                // 这是MediaId（32位字符且不是HTTP URL）
                videoClip.put("MediaId", videoUrl);
                log.debug("使用MediaId: {}", videoUrl);
            } else {
                // 这是URL
                videoClip.put("MediaURL", videoUrl);
                log.debug("使用MediaURL: {}", videoUrl);
            }

            videoClip.put("In", 0);
            videoClip.put("Out", clip.get("duration"));
            videoClip.put("TimelineIn", currentTime);
            videoClip.put("TimelineOut", currentTime + (Double) clip.get("duration"));

            videoTrackClips.add(videoClip);
            currentTime += (Double) clip.get("duration");
        }

        videoTrack.put("VideoTrackClips", videoTrackClips);
        timeline.put("VideoTracks", List.of(videoTrack));

        return timeline.toJSONString();
    }

    /**
     * 调用阿里云云剪辑API创建工程 - 已废弃，使用submitMediaProducingJobWithTimeline替代
     * @deprecated 使用 submitMediaProducingJobWithTimeline 替代
     */
    @Deprecated
    private Map<String, Object> callIceCreateProject(Map<String, Object> params) {
        try {
            log.info("开始调用阿里云ICE API创建云剪辑工程，参数: {}", params);

            // 创建请求对象
            com.aliyun.ice20201109.models.CreateEditingProjectRequest request = new com.aliyun.ice20201109.models.CreateEditingProjectRequest();

            // 设置请求参数
            if (params.containsKey("Title")) {
                request.setTitle(params.get("Title").toString());
            }
            if (params.containsKey("Timeline")) {
                request.setTimeline(params.get("Timeline").toString());
            }
            if (params.containsKey("Description")) {
                request.setDescription(params.get("Description").toString());
                // 当使用Timeline时，必须设置TemplateType为Timeline
                request.setTemplateType("Timeline");
            }

            // 调用阿里云ICE API
            com.aliyun.ice20201109.models.CreateEditingProjectResponse response = iceClientAK.createEditingProject(request);

            Map<String, Object> result = new HashMap<>();
            if (response != null && response.getBody() != null) {
                com.aliyun.ice20201109.models.CreateEditingProjectResponseBody body = response.getBody();

                // 获取ProjectId - 根据阿里云ICE API文档
                String projectId = null;
                try {
                    // 检查响应体是否包含Project对象
                    if (body.getProject() != null) {
                        projectId = body.getProject().getProjectId();
                        log.info("通过Project对象获取ProjectId: {}", projectId);
                    } else {
                        log.warn("响应体中没有Project对象，响应内容: {}", body.toString());
                        // 如果没有Project对象，可能ProjectId直接在响应体中
                        try {
                            java.lang.reflect.Field field = body.getClass().getDeclaredField("projectId");
                            field.setAccessible(true);
                            projectId = (String) field.get(body);
                            log.info("通过反射获取ProjectId: {}", projectId);
                        } catch (Exception e) {
                            log.error("无法获取ProjectId，创建工程可能失败", e);
                            throw new ServiceException("创建云剪辑工程失败：无法获取ProjectId");
                        }
                    }
                } catch (Exception e) {
                    log.error("获取ProjectId时发生异常", e);
                    throw new ServiceException("创建云剪辑工程失败：" + e.getMessage());
                }

                result.put("projectId", projectId);
                result.put("ProjectId", projectId);
                result.put("RequestId", body.getRequestId());
                result.put("message", "云剪辑工程创建成功");

                log.info("云剪辑工程创建成功，ProjectId: {}", projectId);
                log.info("Timeline内容: {}", params.get("Timeline"));
                log.info("响应体信息: {}", body.toString());
            }

            return result;

        } catch (Exception e) {
            log.error("调用云剪辑API创建工程失败", e);
            throw new ServiceException("调用云剪辑API创建工程失败: " + e.getMessage());
        }
    }

    /**
     * 提交导出任务 - 已废弃，使用submitMediaProducingJobWithTimeline替代
     */
    @Deprecated
    private Map<String, Object> submitExportJob(String projectId) {
        try {
            log.info("开始提交导出任务，ProjectId: {}", projectId);

            if (StringUtils.isEmpty(projectId)) {
                throw new ServiceException("ProjectId不能为空");
            }

            // 创建导出任务请求
            com.aliyun.ice20201109.models.SubmitMediaProducingJobRequest request = new com.aliyun.ice20201109.models.SubmitMediaProducingJobRequest();
            request.setProjectId(projectId);

            // 设置输出媒体配置 - MediaURL是必需的参数，使用HTTPS格式
            String outputFileName = "dialogue_video_" + System.currentTimeMillis() + ".mp4";
            String outputMediaURL = "https://szb-pc.oss-cn-beijing.aliyuncs.com/dialogue_videos/" + outputFileName;

            JSONObject outputMediaConfig = new JSONObject();
            outputMediaConfig.put("MediaURL", outputMediaURL);  // 这是必需的参数
            outputMediaConfig.put("Width", 1920);
            outputMediaConfig.put("Height", 1080);
            outputMediaConfig.put("Bitrate", 2000000); // 2Mbps
            outputMediaConfig.put("FrameRate", 25);
            outputMediaConfig.put("Format", "mp4");

            request.setOutputMediaConfig(outputMediaConfig.toJSONString());

            log.info("提交导出任务参数 - ProjectId: {}, OutputMediaURL: {}, OutputMediaConfig: {}",
                projectId, outputMediaURL, outputMediaConfig.toJSONString());

            // 调用阿里云ICE API
            com.aliyun.ice20201109.models.SubmitMediaProducingJobResponse response = iceClientAK.submitMediaProducingJob(request);

            Map<String, Object> result = new HashMap<>();
            if (response != null && response.getBody() != null) {
                com.aliyun.ice20201109.models.SubmitMediaProducingJobResponseBody body = response.getBody();

                result.put("jobId", body.getJobId());
                result.put("JobId", body.getJobId());
                result.put("RequestId", body.getRequestId());
                result.put("message", "导出任务提交成功");
                result.put("outputMediaURL", outputMediaURL);

                log.info("导出任务提交成功，ProjectId: {}, JobId: {}, OutputURL: {}",
                    projectId, body.getJobId(), outputMediaURL);

                // 添加调试信息：立即查询任务状态
                try {
                    Thread.sleep(2000); // 等待2秒让任务开始处理
                    checkJobStatus(body.getJobId());
                } catch (Exception e) {
                    log.warn("查询任务状态失败: {}", e.getMessage());
                }
            }

            return result;

        } catch (Exception e) {
            log.error("提交导出任务失败，ProjectId: {}", projectId, e);
            throw new ServiceException("提交导出任务失败: " + e.getMessage());
        }
    }

    /**
     * 等待媒资分析完成
     *
     * 当OSS文件注册为ICE媒资时，阿里云需要时间来分析媒资（检查视频格式、时长、分辨率等）。
     * 如果在媒资分析完成前就使用，会导致"The specified media is not ready"错误。
     *
     * @param mediaId 媒资ID
     * @return true表示媒资已就绪，false表示超时或失败
     */
    private boolean waitForMediaReady(String mediaId) {
        int maxRetries = MEDIA_READY_MAX_RETRIES; // 最多重试6次
        int retryInterval = MEDIA_READY_RETRY_INTERVAL; // 每次间隔5秒，总计最多等待30秒

        for (int i = 0; i < maxRetries; i++) {
            try {
                log.info("检查媒资状态 - MediaId: {}, 重试次数: {}/{}", mediaId, i + 1, maxRetries);

                // 等待一段时间让媒资分析完成
                Thread.sleep(retryInterval);

                // 通过创建测试工程来验证媒资是否可用（这是最可靠的验证方式）
                if (testMediaAvailability(mediaId)) {
                    log.info("媒资可用性测试成功 - MediaId: {}", mediaId);
                    return true;
                } else {
                    log.warn("媒资可用性测试失败 - MediaId: {}, 继续等待...", mediaId);
                }

            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                log.warn("媒资等待被中断 - MediaId: {}", mediaId);
                return false;
            } catch (Exception e) {
                log.warn("检查媒资状态异常 - MediaId: {}, 错误: {}", mediaId, e.getMessage());
            }
        }

        log.warn("媒资等待超时 - MediaId: {}, 已等待{}秒", mediaId, (maxRetries * retryInterval / 1000));
        return false;
    }

    /**
     * 测试媒资可用性
     * 通过创建一个简单的测试工程来验证媒资是否已经分析完成并可用
     */
    private boolean testMediaAvailability(String mediaId) {
        try {
            com.aliyun.ice20201109.models.CreateEditingProjectRequest testRequest =
                new com.aliyun.ice20201109.models.CreateEditingProjectRequest();
            testRequest.setTitle("MediaTest_" + System.currentTimeMillis());
            testRequest.setTemplateType("Timeline");

            // 创建简单的Timeline测试媒资（只取1秒钟）
            String testTimeline = String.format(
                "{\"VideoTracks\":[{\"VideoTrackClips\":[{\"MediaId\":\"%s\",\"In\":0,\"Out\":1.0,\"TimelineIn\":0.0,\"TimelineOut\":1.0}]}]}",
                mediaId);
            testRequest.setTimeline(testTimeline);

            com.aliyun.ice20201109.models.CreateEditingProjectResponse testResponse =
                iceClientAK.createEditingProject(testRequest);

            return testResponse != null && testResponse.getBody() != null;

        } catch (Exception e) {
            // 如果错误信息包含"not ready"，说明媒资还在分析中
            if (e.getMessage() != null && e.getMessage().contains("not ready")) {
                return false;
            }
            // 其他错误也认为媒资不可用
            log.debug("媒资可用性测试异常 - MediaId: {}, 错误: {}", mediaId, e.getMessage());
            return false;
        }
    }

    /**
     * 直接提交云剪辑合成任务（使用Timeline参数）
     *
     * 根据阿里云ICE官方文档，SubmitMediaProducingJob接口支持三种模式：
     * 1. ProjectId模式：使用已创建的工程
     * 2. Timeline模式：直接传入时间线（当前使用的模式）
     * 3. TemplateId模式：使用模板
     *
     * 当前使用Timeline模式，这是最直接和推荐的方式，避免了工程创建和任务提交之间的潜在冲突。
     *
     * @param timeline 云剪辑时间线JSON字符串
     * @param title 视频标题
     * @param description 视频描述
     * @return 包含任务信息的结果Map
     */
    private Map<String, Object> submitMediaProducingJobWithTimeline(String timeline, String title, String description) {
        try {
            log.info("开始提交云剪辑合成任务，Timeline: {}", timeline);

            // 创建请求对象
            com.aliyun.ice20201109.models.SubmitMediaProducingJobRequest request =
                new com.aliyun.ice20201109.models.SubmitMediaProducingJobRequest();

            // 设置Timeline参数（核心参数）
            request.setTimeline(timeline);

            // 设置输出配置
            String outputMediaURL = generateOutputMediaURL();
            JSONObject outputMediaConfig = createOutputMediaConfig(outputMediaURL);

            request.setOutputMediaConfig(outputMediaConfig.toString());

            // 设置项目元数据（可选）
            if (StringUtils.isNotEmpty(title) || StringUtils.isNotEmpty(description)) {
                JSONObject projectMetadata = new JSONObject();
                if (StringUtils.isNotEmpty(title)) {
                    projectMetadata.put("Title", title);
                }
                if (StringUtils.isNotEmpty(description)) {
                    projectMetadata.put("Description", description);
                }
                request.setProjectMetadata(projectMetadata.toString());
            }

            log.info("提交云剪辑合成任务参数 - Timeline: {}, OutputMediaURL: {}, OutputMediaConfig: {}",
                timeline, outputMediaURL, outputMediaConfig.toString());

            // 调用阿里云ICE API
            com.aliyun.ice20201109.models.SubmitMediaProducingJobResponse response =
                iceClientAK.submitMediaProducingJob(request);

            Map<String, Object> result = new HashMap<>();
            if (response != null && response.getBody() != null) {
                com.aliyun.ice20201109.models.SubmitMediaProducingJobResponseBody body = response.getBody();

                result.put("jobId", body.getJobId());
                result.put("JobId", body.getJobId());
                result.put("RequestId", body.getRequestId());
                result.put("projectId", body.getProjectId()); // 阿里云会自动创建ProjectId
                result.put("ProjectId", body.getProjectId());
                result.put("mediaId", body.getMediaId());
                result.put("MediaId", body.getMediaId());
                result.put("message", "云剪辑合成任务提交成功");
                result.put("outputMediaURL", outputMediaURL);

                log.info("云剪辑合成任务提交成功，ProjectId: {}, JobId: {}, MediaId: {}, OutputURL: {}",
                    body.getProjectId(), body.getJobId(), body.getMediaId(), outputMediaURL);

                // 添加调试信息：立即查询任务状态
                try {
                    Thread.sleep(2000); // 等待2秒让任务开始处理
                    checkJobStatus(body.getJobId());
                } catch (Exception e) {
                    log.warn("查询任务状态失败: {}", e.getMessage());
                }
            }

            return result;

        } catch (Exception e) {
            log.error("提交云剪辑合成任务失败", e);
            throw new ServiceException("提交云剪辑合成任务失败: " + e.getMessage());
        }
    }

    /**
     * 生成输出媒体URL
     *
     * @return 生成的OSS输出URL
     */
    private String generateOutputMediaURL() {
        return String.format("https://szb-pc.oss-cn-beijing.aliyuncs.com/%s/dialogue_video_%d.%s",
            OSS_DIALOGUE_VIDEO_PATH, System.currentTimeMillis(), DEFAULT_VIDEO_FORMAT);
    }

    /**
     * 创建输出媒体配置
     *
     * @param outputMediaURL 输出媒体URL
     * @return 输出媒体配置JSON对象
     */
    private JSONObject createOutputMediaConfig(String outputMediaURL) {
        JSONObject config = new JSONObject();
        config.put("MediaURL", outputMediaURL);
        config.put("Width", DEFAULT_VIDEO_WIDTH);
        config.put("Height", DEFAULT_VIDEO_HEIGHT);
        config.put("Bitrate", DEFAULT_VIDEO_BITRATE);
        config.put("FrameRate", DEFAULT_VIDEO_FRAMERATE);
        config.put("Format", DEFAULT_VIDEO_FORMAT);
        return config;
    }

    /**
     * 检查任务状态并输出详细信息
     */
    private void checkJobStatus(String jobId) {
        try {
            log.info("开始检查任务状态，JobId: {}", jobId);

            // 创建查询请求
            com.aliyun.ice20201109.models.GetMediaProducingJobRequest request =
                new com.aliyun.ice20201109.models.GetMediaProducingJobRequest();
            request.setJobId(jobId);

            // 调用阿里云ICE API查询任务状态
            com.aliyun.ice20201109.models.GetMediaProducingJobResponse response =
                iceClientAK.getMediaProducingJob(request);

            if (response != null && response.getBody() != null) {
                com.aliyun.ice20201109.models.GetMediaProducingJobResponseBody body = response.getBody();

                if (body.getMediaProducingJob() != null) {
                    String status = body.getMediaProducingJob().getStatus();
                    String message = body.getMediaProducingJob().getMessage();
                    String createTime = body.getMediaProducingJob().getCreateTime();
                    String modifiedTime = body.getMediaProducingJob().getModifiedTime();

                    log.info("任务详细状态 - JobId: {}, Status: {}, Message: {}, CreateTime: {}, ModifiedTime: {}",
                        jobId, status, message, createTime, modifiedTime);

                    // 输出完整的任务信息
                    try {
                        log.info("完整任务信息: {}", body.getMediaProducingJob().toString());
                    } catch (Exception e) {
                        log.warn("无法输出完整任务信息: {}", e.getMessage());
                    }

                    // 如果任务失败，输出详细错误信息
                    if ("Failed".equalsIgnoreCase(status)) {
                        log.error("任务失败详情 - JobId: {}, 错误信息: {}", jobId, message);

                        // 尝试获取更多错误详情
                        try {
                            if (body.getMediaProducingJob().getTimeline() != null) {
                                log.error("失败任务的Timeline: {}", body.getMediaProducingJob().getTimeline());
                            }
                        } catch (Exception e) {
                            log.warn("无法获取Timeline信息: {}", e.getMessage());
                        }
                    }
                } else {
                    log.warn("任务信息为空，JobId: {}", jobId);
                }
            } else {
                log.warn("查询任务状态响应为空，JobId: {}", jobId);
            }

        } catch (Exception e) {
            log.error("检查任务状态异常，JobId: {}, 错误: {}", jobId, e.getMessage());
        }
    }

    // 已弃用
    public Map<String, Object> createDialogueVideoClipByGroupId(String dialogueGroupId, String title, String description) {
        if (StringUtils.isEmpty(dialogueGroupId)) {
            throw new ServiceException("对话组ID不能为空");
        }

        try {
            // 根据对话组ID查找对应的已完成任务
            List<PlatformVideo> groupTasks = findTasksByDialogueGroupId(dialogueGroupId);

            if (groupTasks.isEmpty()) {
                throw new ServiceException("未找到对话组ID为 " + dialogueGroupId + " 的任务");
            }

            // 检查所有任务是否都已完成
            validateTasksCompleted(groupTasks);

            // 构建视频片段列表
            List<Map<String, Object>> videoClips = buildVideoClips(groupTasks);

            log.info("准备进行云剪辑合成，使用的数字人视频素材:");
            for (int i = 0; i < videoClips.size(); i++) {
                Map<String, Object> clip = videoClips.get(i);
                log.info("  素材{}: 任务ID={}, 视频路径={}, 时长={}秒",
                    i+1, clip.get("taskId"), clip.get("videoUrl"), clip.get("duration"));
            }

            // 构建云剪辑Timeline
            String timeline = buildVideoClipTimeline(videoClips);
            log.info("云剪辑Timeline构建完成，包含{}个视频片段", videoClips.size());

            // 构建云剪辑请求参数
            Map<String, Object> params = new HashMap<>();
            params.put("Title", StringUtils.isNotEmpty(title) ? title : "数字人对话视频_" + dialogueGroupId);
            params.put("Timeline", timeline);
            if (StringUtils.isNotEmpty(description)) {
                params.put("Description", description);
            }

            // 直接提交云剪辑合成任务（使用Timeline参数）
            Map<String, Object> result = submitMediaProducingJobWithTimeline(timeline, title, description);

            // 简单标记任务已进行云剪辑合成（不创建额外的数据库记录）
            markTasksAsClippedSimple(groupTasks, result);

            log.info("云剪辑合成任务创建成功，对话组ID: {}, ProjectId: {}, JobId: {}",
                    dialogueGroupId, result.get("projectId"), result.get("jobId"));

            return result;
        } catch (Exception e) {
            log.error("根据对话组ID进行云剪辑合成失败: {}", dialogueGroupId, e);
            throw new ServiceException("云剪辑合成失败: " + e.getMessage());
        }
    }

    /**
     * 根据对话组ID查找任务
     */
    private List<PlatformVideo> findTasksByDialogueGroupId(String dialogueGroupId) {
        // 查询所有已完成的任务
        List<PlatformVideo> allCompletedTasks = platformVideoMapper.selectCompletedDialogueTasks();

        // 筛选出指定对话组的任务
        return allCompletedTasks.stream()
            .filter(task -> {
                String taskGroupId = extractDialogueGroupId(task);
                return dialogueGroupId.equals(taskGroupId);
            })
            .sorted((t1, t2) -> {
                Integer order1 = extractDialogueOrder(t1);
                Integer order2 = extractDialogueOrder(t2);
                // 处理null值，null值排在最后，如果都是null则按ID排序
                if (order1 == null && order2 == null) {
                    return t1.getId().compareTo(t2.getId());
                }
                if (order1 == null) return 1;
                if (order2 == null) return -1;
                return order1.compareTo(order2);
            })
            .collect(Collectors.toList());
    }

    /**
     * 验证任务是否都已完成
     */
    private void validateTasksCompleted(List<PlatformVideo> tasks) {
        for (PlatformVideo task : tasks) {
            if (!"3".equals(task.getStatus())) {
                throw new ServiceException("任务ID " + task.getId() + " 尚未完成，当前状态: " + task.getStatus());
            }
            if (StringUtils.isEmpty(task.getResultVideo())) {
                throw new ServiceException("任务ID " + task.getId() + " 的结果视频为空");
            }
        }
    }



    /**
     * 从任务中提取对话组ID
     */
    private String extractDialogueGroupId(PlatformVideo task) {
        try {
            if (StringUtils.isNotEmpty(task.getOperation())) {
                JSONObject operation = JSONObject.parseObject(task.getOperation());
                return operation.getString("dialogueGroupId");
            }
        } catch (Exception e) {
            log.warn("解析任务操作JSON失败，任务ID: {}", task.getId());
        }
        return null;
    }

    /**
     * 从任务中提取对话顺序
     */
    private Integer extractDialogueOrder(PlatformVideo task) {
        try {
            if (StringUtils.isNotEmpty(task.getOperation())) {
                JSONObject operation = JSONObject.parseObject(task.getOperation());
                Integer order = operation.getInteger("dialogueOrder");
                return order != null ? order : 0;
            }
        } catch (Exception e) {
            log.warn("解析任务对话顺序失败，任务ID: {}", task.getId());
        }
        return 0;
    }

    /**
     * 简单标记任务已进行云剪辑合成（不创建额外的数据库记录）
     */
    private void markTasksAsClippedSimple(List<PlatformVideo> groupTasks, Map<String, Object> clipResult) {
        for (PlatformVideo task : groupTasks) {
            try {
                JSONObject operation = StringUtils.isNotEmpty(task.getOperation())
                    ? JSONObject.parseObject(task.getOperation())
                    : new JSONObject();

                operation.put("videoClipped", true);
                operation.put("clipJobId", clipResult.get("jobId"));
                operation.put("clipProjectId", clipResult.get("projectId"));
                operation.put("clipTime", new Date());

                task.setOperation(operation.toJSONString());
                platformVideoMapper.updatePlatformVideo(task);

                log.info("任务云剪辑状态标记成功，任务ID: {}, JobId: {}", task.getId(), clipResult.get("jobId"));
            } catch (Exception e) {
                log.error("标记任务云剪辑状态失败，任务ID: {}", task.getId(), e);
            }
        }
    }





    @Override
    public Map<String, Object> checkDialogueGroupStatus(String dialogueGroupId) {
        if (StringUtils.isEmpty(dialogueGroupId)) {
            throw new ServiceException("对话组ID不能为空");
        }

        Map<String, Object> result = new HashMap<>();
        result.put("dialogueGroupId", dialogueGroupId);

        try {
            // 查找对话组的所有任务
            List<PlatformVideo> groupTasks = findTasksByDialogueGroupId(dialogueGroupId);

            if (groupTasks.isEmpty()) {
                result.put("status", "NOT_FOUND");
                result.put("message", "未找到对话组ID为 " + dialogueGroupId + " 的任务");
                result.put("canClip", false);
                return result;
            }

            // 统计任务状态
            int totalTasks = groupTasks.size();
            int completedTasks = 0;
            int failedTasks = 0;
            int processingTasks = 0;
            int pendingTasks = 0;

            List<Map<String, Object>> taskDetails = new ArrayList<>();

            for (PlatformVideo task : groupTasks) {
                Map<String, Object> taskInfo = new HashMap<>();
                taskInfo.put("taskId", task.getId());
                taskInfo.put("status", task.getStatus());
                taskInfo.put("order", extractDialogueOrder(task));

                // 从operation中提取更多信息
                if (StringUtils.isNotEmpty(task.getOperation())) {
                    try {
                        JSONObject operation = JSONObject.parseObject(task.getOperation());
                        taskInfo.put("speakerName", operation.getString("speakerName"));
                        taskInfo.put("text", operation.getString("text"));
                        taskInfo.put("videoClipped", operation.getBooleanValue("videoClipped"));
                    } catch (Exception e) {
                        log.warn("解析任务操作JSON失败，任务ID: {}", task.getId());
                    }
                }

                // 统计状态
                switch (task.getStatus()) {
                    case "3": // 完成
                        completedTasks++;
                        taskInfo.put("statusText", "已完成");
                        break;
                    case "4": // 失败
                        failedTasks++;
                        taskInfo.put("statusText", "失败");
                        break;
                    case "2": // 处理中
                        processingTasks++;
                        taskInfo.put("statusText", "处理中");
                        break;
                    case "1": // 待处理
                        pendingTasks++;
                        taskInfo.put("statusText", "待处理");
                        break;
                    default:
                        taskInfo.put("statusText", "未知状态");
                }

                taskDetails.add(taskInfo);
            }

            // 判断整体状态
            boolean allCompleted = completedTasks == totalTasks;
            boolean hasFailures = failedTasks > 0;
            boolean canClip = allCompleted && !hasFailures;

            String overallStatus;
            String message;

            if (allCompleted) {
                overallStatus = "COMPLETED";
                message = "所有任务已完成，可以进行云剪辑合成";
            } else if (hasFailures) {
                overallStatus = "FAILED";
                message = String.format("有 %d 个任务失败，无法进行云剪辑合成", failedTasks);
            } else if (processingTasks > 0) {
                overallStatus = "PROCESSING";
                message = String.format("有 %d 个任务正在处理中，请稍后再试", processingTasks);
            } else {
                overallStatus = "PENDING";
                message = String.format("有 %d 个任务待处理", pendingTasks);
            }

            // 检查是否已经进行过云剪辑
            boolean alreadyClipped = groupTasks.stream().anyMatch(task -> {
                try {
                    if (StringUtils.isNotEmpty(task.getOperation())) {
                        JSONObject operation = JSONObject.parseObject(task.getOperation());
                        return operation.getBooleanValue("videoClipped");
                    }
                } catch (Exception e) {
                    // 忽略解析错误
                }
                return false;
            });

            if (alreadyClipped) {
                canClip = false;
                message += "（已进行过云剪辑合成）";
            }

            result.put("status", overallStatus);
            result.put("message", message);
            result.put("canClip", canClip);
            result.put("totalTasks", totalTasks);
            result.put("completedTasks", completedTasks);
            result.put("failedTasks", failedTasks);
            result.put("processingTasks", processingTasks);
            result.put("pendingTasks", pendingTasks);
            result.put("alreadyClipped", alreadyClipped);
            result.put("taskDetails", taskDetails);

            return result;

        } catch (Exception e) {
            log.error("检查对话组状态失败: {}", dialogueGroupId, e);
            result.put("status", "ERROR");
            result.put("message", "检查状态失败: " + e.getMessage());
            result.put("canClip", false);
            return result;
        }
    }

    @Override
    public Map<String, Object> createDialogueVideoClipByTemplate(String dialogueGroupId, String templateId, String title, String description) {
        if (StringUtils.isEmpty(dialogueGroupId)) {
            throw new ServiceException("对话组ID不能为空");
        }

        // 注意：templateId 可以为空，表示使用智能选择

        try {
            log.info("开始使用模板进行对话视频合成，对话组ID: {}, 模板ID: {}", dialogueGroupId, templateId);
            log.info("模板ID是否为空: {}", StringUtils.isEmpty(templateId));

            // 根据对话组ID查找对应的已完成任务
            List<PlatformVideo> groupTasks = findTasksByDialogueGroupId(dialogueGroupId);

            if (groupTasks.isEmpty()) {
                throw new ServiceException("未找到对话组ID为 " + dialogueGroupId + " 的任务");
            }

            log.info("找到对话组 {} 的任务数量: {}", dialogueGroupId, groupTasks.size());

            // 验证任务是否都已完成
            validateTasksCompleted(groupTasks);

            // 如果没有提供模板ID，智能选择最适合的模板
            String finalTemplateId = templateId;
            if (StringUtils.isEmpty(finalTemplateId)) {
                log.info("模板ID为空，开始智能选择模板，视频数量: {}", groupTasks.size());
                finalTemplateId = selectBestDialogueTemplate(groupTasks.size());
                if (StringUtils.isEmpty(finalTemplateId)) {
                    throw new ServiceException("无法找到合适的对话模板");
                }
                log.info("智能选择模板完成: {}", finalTemplateId);
            } else {
                log.info("使用指定的模板ID: {}", finalTemplateId);
            }

            // 构建clipsParam，将视频文件路径映射到模板参数
            JSONObject clipsParam = buildClipsParamForTemplate(groupTasks, finalTemplateId);

            log.info("构建的clipsParam: {}", clipsParam.toJSONString());

            // 调用现有的剪辑服务
            Map<String, Object> result = callMediaProducingServiceDirectly(finalTemplateId, clipsParam.toJSONString(), groupTasks);

            // 标记任务已进行云剪辑合成
            markTasksAsClippedWithTemplate(groupTasks, result, templateId);

            log.info("使用模板 {} 的对话视频合成任务创建成功，对话组ID: {}, JobId: {}",
                    templateId, dialogueGroupId, result.get("jobId"));

            return result;
        } catch (Exception e) {
            log.error("使用模板进行对话视频合成失败，对话组ID: {}, 模板ID: {}", dialogueGroupId, templateId, e);
            throw new ServiceException("对话视频合成失败: " + e.getMessage());
        }
    }

    /**
     * 构建模板所需的clipsParam参数
     *
     * 根据现有剪辑服务的逻辑，clipsParam应该是一个JSON对象，
     * 其中键是模板中的参数名，值为"mediaId"（占位符），
     * 实际的视频文件会在服务中被替换为OSS URL
     */
    private JSONObject buildClipsParamForTemplate(List<PlatformVideo> groupTasks, String templateId) {
        JSONObject clipsParam = new JSONObject();

        // 获取模板的参数映射规则
        Map<String, String> paramMapping = getTemplateParamMapping(templateId);

        // 根据任务数量和模板参数映射构建参数
        for (int i = 0; i < groupTasks.size() && i < paramMapping.size(); i++) {
            String paramKey = paramMapping.get("param" + (i + 1));
            if (paramKey != null) {
                clipsParam.put(paramKey, "mediaId"); // 使用"mediaId"作为占位符
            }
        }

        log.info("为 {} 个视频构建clipsParam (模板: {}): {}", groupTasks.size(), templateId, clipsParam.toJSONString());
        return clipsParam;
    }

    /**
     * 直接调用现有的剪辑服务
     *
     * 这个方法模拟现有剪辑服务的逻辑，但是不使用文件上传，
     * 而是直接使用已有的视频文件OSS URL
     */
    private Map<String, Object> callMediaProducingServiceDirectly(String templateId, String clipsParam, List<PlatformVideo> groupTasks) {
        try {
            log.info("开始调用剪辑服务，模板ID: {}, clipsParam: {}", templateId, clipsParam);

            // 创建请求对象
            com.aliyun.ice20201109.models.SubmitMediaProducingJobRequest request =
                new com.aliyun.ice20201109.models.SubmitMediaProducingJobRequest();

            // 设置模板ID
            request.setTemplateId(templateId);

            // 构建实际的clipsParam（将占位符替换为真实的OSS URL）
            JSONObject actualClipsParam = buildActualClipsParam(clipsParam, groupTasks);
            request.setClipsParam(actualClipsParam.toJSONString());

            // 设置输出配置
            JSONObject outputMediaConfig = new JSONObject();
            String outputPath = generateOutputPath();
            outputMediaConfig.put("MediaURL", outputPath);
            request.setOutputMediaConfig(outputMediaConfig.toJSONString());

            // 设置用户数据（可选的回调配置）
            JSONObject userData = new JSONObject();
            // 这里可以添加回调地址等配置
            request.setUserData(userData.toJSONString());

            log.info("提交剪辑任务参数 - TemplateId: {}, ClipsParam: {}, OutputPath: {}",
                templateId, actualClipsParam.toJSONString(), outputPath);

            // 调用阿里云ICE API
            com.aliyun.ice20201109.models.SubmitMediaProducingJobResponse response =
                iceClientAK.submitMediaProducingJob(request);

            if (response != null && response.getBody() != null) {
                com.aliyun.ice20201109.models.SubmitMediaProducingJobResponseBody body = response.getBody();

                Map<String, Object> result = new HashMap<>();
                result.put("requestId", body.getRequestId());
                result.put("projectId", body.getProjectId());
                result.put("jobId", body.getJobId());
                result.put("mediaId", body.getMediaId());
                result.put("vodMediaId", body.getVodMediaId());
                result.put("outputPath", outputPath);
                result.put("message", "模板剪辑任务提交成功");

                log.info("剪辑任务提交成功 - JobId: {}, ProjectId: {}, MediaId: {}",
                    body.getJobId(), body.getProjectId(), body.getMediaId());

                return result;
            } else {
                throw new ServiceException("剪辑服务响应为空");
            }

        } catch (Exception e) {
            log.error("调用剪辑服务失败", e);
            throw new ServiceException("调用剪辑服务失败: " + e.getMessage());
        }
    }

    /**
     * 构建实际的clipsParam（将占位符替换为真实的OSS URL）
     */
    private JSONObject buildActualClipsParam(String clipsParam, List<PlatformVideo> groupTasks) {
        JSONObject originalParam = JSONObject.parseObject(clipsParam);
        JSONObject actualParam = new JSONObject();

        int taskIndex = 0;
        for (Map.Entry<String, Object> entry : originalParam.entrySet()) {
            if ("mediaId".equals(entry.getValue()) && taskIndex < groupTasks.size()) {
                PlatformVideo task = groupTasks.get(taskIndex);
                String videoPath = task.getResultVideo();

                // 转换为标准的OSS URL（移除签名参数）
                String cleanOssUrl = convertToCleanOssUrl(videoPath);
                actualParam.put(entry.getKey(), cleanOssUrl);

                log.info("参数映射: {} -> {}", entry.getKey(), cleanOssUrl);
                taskIndex++;
            } else {
                // 保持其他参数不变
                actualParam.put(entry.getKey(), entry.getValue());
            }
        }

        return actualParam;
    }

    /**
     * 转换为干净的OSS URL（移除签名参数）
     */
    private String convertToCleanOssUrl(String videoPath) {
        if (StringUtils.isEmpty(videoPath)) {
            throw new ServiceException("视频路径为空");
        }

        // 如果已经是完整的OSS URL，直接处理
        if (videoPath.startsWith("https://")) {
            // 移除URL中的签名参数
            int questionMarkIndex = videoPath.indexOf('?');
            if (questionMarkIndex > 0) {
                return videoPath.substring(0, questionMarkIndex);
            }
            return videoPath;
        }

        // 如果是相对路径，构建完整的OSS URL
        return "https://szb-pc.oss-cn-beijing.aliyuncs.com/" + videoPath;
    }

    /**
     * 生成输出文件路径
     */
    private String generateOutputPath() {
        return String.format("https://szb-pc.oss-cn-beijing.aliyuncs.com/%s/template_dialogue_video_%d.%s",
            OSS_DIALOGUE_VIDEO_PATH, System.currentTimeMillis(), DEFAULT_VIDEO_FORMAT);
    }

    /**
     * 标记任务已使用模板进行云剪辑合成
     */
    private void markTasksAsClippedWithTemplate(List<PlatformVideo> groupTasks, Map<String, Object> clipResult, String templateId) {
        for (PlatformVideo task : groupTasks) {
            try {
                JSONObject operation = StringUtils.isNotEmpty(task.getOperation())
                    ? JSONObject.parseObject(task.getOperation())
                    : new JSONObject();

                operation.put("videoClipped", true);
                operation.put("clipJobId", clipResult.get("jobId"));
                operation.put("clipProjectId", clipResult.get("projectId"));
                operation.put("clipTemplateId", templateId);
                operation.put("clipTime", new Date());
                operation.put("clipMethod", "template");

                task.setOperation(operation.toJSONString());
                platformVideoMapper.updatePlatformVideo(task);

                log.info("任务模板云剪辑状态标记成功，任务ID: {}, JobId: {}, TemplateId: {}",
                    task.getId(), clipResult.get("jobId"), templateId);
            } catch (Exception e) {
                log.error("标记任务模板云剪辑状态失败，任务ID: {}", task.getId(), e);
            }
        }
    }



    @Override
    public boolean validateTemplate(String templateId) {
        try {
            log.info("验证模板是否存在: {}", templateId);

            // 使用更简单的方式验证模板：调用GetTemplate接口
            com.aliyun.ice20201109.models.GetTemplateRequest request =
                new com.aliyun.ice20201109.models.GetTemplateRequest();
            request.setTemplateId(templateId);

            com.aliyun.ice20201109.models.GetTemplateResponse response =
                iceClientAK.getTemplate(request);

            if (response != null && response.getBody() != null && response.getBody().getTemplate() != null) {
                log.info("模板 {} 存在", templateId);
                return true;
            } else {
                log.warn("模板 {} 不存在", templateId);
                return false;
            }
        } catch (Exception e) {
            log.warn("验证模板 {} 时发生异常: {}", templateId, e.getMessage());
            // 如果是模板不存在的异常，返回false
            if (e.getMessage() != null && e.getMessage().contains("TemplateNotFound")) {
                return false;
            }
            return false;
        }
    }

    /**
     * 智能选择最适合的对话模板
     *
     * @param videoCount 视频数量
     * @return 模板ID
     */
    private String selectBestDialogueTemplate(int videoCount) {
        try {
            log.info("开始智能选择对话模板，视频数量: {}", videoCount);

            String selectedTemplateId;

            if (videoCount == 2) {
                // 2人对话，优先使用2人对话模板
                selectedTemplateId = DIALOGUE_TEMPLATES.get("2_PERSON_DIALOGUE");
                log.info("选择2人对话模板: {}", selectedTemplateId);
            } else if (videoCount >= 3) {
                // 多人对话，使用多人对话模板
                selectedTemplateId = DIALOGUE_TEMPLATES.get("MULTI_PERSON_DIALOGUE");
                log.info("选择多人对话模板: {}", selectedTemplateId);
            } else {
                // 其他情况使用备用模板
                selectedTemplateId = DIALOGUE_TEMPLATES.get("FALLBACK_TEMPLATE");
                log.info("选择备用模板: {}", selectedTemplateId);
            }

            // 验证模板是否存在
            log.info("开始验证模板: {}", selectedTemplateId);
            if (validateTemplate(selectedTemplateId)) {
                log.info("模板验证成功，使用模板: {}", selectedTemplateId);
                return selectedTemplateId;
            } else {
                log.warn("选择的模板 {} 不存在，尝试备用模板", selectedTemplateId);
                String fallbackTemplate = DIALOGUE_TEMPLATES.get("FALLBACK_TEMPLATE");
                if (!selectedTemplateId.equals(fallbackTemplate) && validateTemplate(fallbackTemplate)) {
                    log.info("使用备用模板: {}", fallbackTemplate);
                    return fallbackTemplate;
                } else {
                    log.error("所有预定义模板都不可用，请检查模板配置");
                    // 返回第一个模板ID，让后续流程处理错误
                    return selectedTemplateId;
                }
            }

        } catch (Exception e) {
            log.error("智能选择模板失败", e);
            // 返回默认模板
            return DIALOGUE_TEMPLATES.get("FALLBACK_TEMPLATE");
        }
    }

    /**
     * 获取模板的参数映射规则
     *
     * @param templateId 模板ID
     * @return 参数映射规则
     */
    private Map<String, String> getTemplateParamMapping(String templateId) {
        Map<String, String> mapping = new HashMap<>();

        if ("29bcaa98249349b79cdb693b21ba3860".equals(templateId)) {
            // 测试数据模板的参数映射
            mapping.put("param1", "1cc37b");
            mapping.put("param2", "823b59");
            mapping.put("param3", "e5f45b");
        } else if ("a8ca479ecb554b9baefd8040f4592127".equals(templateId)) {
            // 默认模板的参数映射
            mapping.put("param1", "4d9f4b");
            mapping.put("param2", "c6ce51");
            mapping.put("param3", "c1d795");
        } else {
            // 通用参数映射
            mapping.put("param1", "media1");
            mapping.put("param2", "media2");
            mapping.put("param3", "media3");
        }

        return mapping;
    }

    @Override
    public Map<String, Object> getAvailableDialogueTemplates() {
        Map<String, Object> result = new HashMap<>();

        // 返回预定义的模板信息
        List<Map<String, Object>> templates = new ArrayList<>();

        for (Map.Entry<String, String> entry : DIALOGUE_TEMPLATES.entrySet()) {
            Map<String, Object> template = new HashMap<>();
            template.put("key", entry.getKey());
            template.put("templateId", entry.getValue());
            template.put("name", getTemplateDisplayName(entry.getKey()));
            template.put("description", getTemplateDescription(entry.getKey()));
            templates.add(template);
        }

        result.put("templates", templates);
        result.put("total", templates.size());
        result.put("message", "获取可用模板列表成功");

        return result;
    }

    /**
     * 获取模板显示名称
     */
    private String getTemplateDisplayName(String key) {
        switch (key) {
            case "2_PERSON_DIALOGUE":
                return "2人对话模板";
            case "MULTI_PERSON_DIALOGUE":
                return "多人对话模板";
            case "FALLBACK_TEMPLATE":
                return "备用模板";
            default:
                return "未知模板";
        }
    }

    /**
     * 获取模板描述
     */
    private String getTemplateDescription(String key) {
        switch (key) {
            case "2_PERSON_DIALOGUE":
                return "适用于2人对话场景，支持3个媒体参数";
            case "MULTI_PERSON_DIALOGUE":
                return "适用于3人及以上对话场景，支持3个媒体参数";
            case "FALLBACK_TEMPLATE":
                return "备用模板，当其他模板不可用时使用";
            default:
                return "模板描述未定义";
        }
    }
}
