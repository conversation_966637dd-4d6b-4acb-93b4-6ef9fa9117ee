package com.ruoyi.video.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.alibaba.fastjson2.JSON;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.video.dto.CreateEditingProjectRequestDTO;
import com.ruoyi.video.dto.SubmitProjectExportRequestDTO;
import com.ruoyi.video.service.IVideoProjectService;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;

/**
 * 云剪辑工程管理 前端控制器
 * <p>
 * 负责接收和处理与阿里云视频剪辑工程相关的HTTP请求。
 * 提供RESTful API，用于查询、创建、删除和获取剪辑工程详情。
 * </p>
 *
 * <AUTHOR>
 * @date 2025-07-03
 */
@Slf4j
@RestController
@RequestMapping("/video/project")
@Tag(name = "【云剪辑工程】管理")
public class VideoProjectController {

    @Autowired
    private IVideoProjectService videoProjectService;

    /**
     * 获取云剪辑工程列表
     * <p>
     * 通过调用阿里云ICE(智能媒体服务)的 ListEditingProjects 接口，获取符合条件的剪辑工程列表。
     * 支持根据关键字、状态进行筛选，并支持分页。
     * </p>
     *
     * @param keyword    可选。搜索关键字，用于模糊匹配工程的标题和描述。
     * @param status     可选。筛选特定状态的工程。有效值：Draft, Editing, Producing, Produced, ProduceFailed
     * @param nextToken  可选。用于分页查询。当一次查询未能返回所有结果时，接口会返回此token，下次请求时传入即可获取下一页数据。
     * @param maxResults 可选。每页返回的最大记录数，默认由阿里云接口决定。
     * @return 返回一个包含了剪辑工程列表和分页信息的AjaxResult对象。
     */
    @Operation(summary = "获取云剪辑工程列表")
    @GetMapping("/listEditingProjects")
    public AjaxResult listEditingProjects(
            @RequestParam(required = false) String keyword,
            @RequestParam(required = false) String status,
            @RequestParam(required = false) String nextToken,
            @RequestParam(required = false) Integer maxResults) {
        try {
            String result = videoProjectService.listEditingProjects(keyword, status, nextToken, maxResults);
            Object data = JSON.parse(result);

            // 检查是否是空结果
            if (result.contains("\"ProjectList\":[]")) {
                return AjaxResult.success("查询剪辑工程列表成功，暂无数据", data);
            }

            return AjaxResult.success("查询剪辑工程列表成功", data);
        } catch (Exception e) {
            log.error("查询剪辑工程列表失败", e);
            return AjaxResult.error("查询剪辑工程列表失败: " + e.getMessage());
        }
    }

    /**
     * 删除云剪辑工程
     * <p>
     * 支持根据 ProjectId 批量删除。
     * 多个ID之间通过逗号拼接成一个字符串传入。
     * </p>
     *
     * @param projectIds 要删除的工程ID字符串，多个ID以逗号分隔。
     * @return 返回操作结果，成功时包含阿里云返回的信息。
     */
    @Operation(summary = "删除云剪辑工程")
    @PostMapping("/deleteEditingProjects")
    public AjaxResult deleteEditingProjects(@RequestParam String projectIds) {
        if (projectIds == null || projectIds.trim().isEmpty()) {
            return AjaxResult.error("ProjectIds 不能为空");
        }

        try {
            String result = videoProjectService.deleteEditingProjects(projectIds);
            return AjaxResult.success("删除剪辑工程请求成功", JSON.parse(result));
        } catch (Exception e) {
            return AjaxResult.error("删除剪辑工程失败: " + e.getMessage());
        }
    }

    /**
     * 创建云剪辑工程
     * <p>
     * 接收包含工程详情的JSON对象，创建一个新的云剪辑工程。
     * </p>
     *
     * @param requestDTO 包含创建工程所需全部参数的数据传输对象。
     * @return 返回操作结果，成功时包含阿里云返回的新建工程信息。
     */
    @Operation(summary = "创建云剪辑工程")
    @PostMapping("/create")
    public AjaxResult createEditingProject(@RequestBody CreateEditingProjectRequestDTO requestDTO) {
        if (requestDTO == null) {
            return AjaxResult.error("请求参数不能为空");
        }

        try {
            String result = videoProjectService.createEditingProject(requestDTO);
            return AjaxResult.success("创建剪辑工程成功", JSON.parse(result));
        } catch (Exception e) {
            return AjaxResult.error("创建剪辑工程失败: " + e.getMessage());
        }
    }

    /**
     * 获取单个云剪辑工程的详细信息
     *
     * @param projectId     云剪辑工程ID, 从URL路径中获取
     * @param requestSource 可选的请求来源标识, 如 "WebSDK"
     * @return 返回包含工程详细信息的AjaxResult对象
     */
    @Operation(summary = "获取单个云剪辑工程的详细信息")
    @GetMapping("/{projectId}")
    public AjaxResult getEditingProject(
            @PathVariable String projectId,
            @RequestParam(required = false) String requestSource) {
        if (projectId == null || projectId.trim().isEmpty()) {
            return AjaxResult.error("projectId 不能为空");
        }

        try {
            String result = videoProjectService.getEditingProject(projectId, requestSource);
            return AjaxResult.success("获取剪辑工程详情成功", JSON.parse(result));
        } catch (Exception e) {
            return AjaxResult.error("获取剪辑工程详情失败: " + e.getMessage());
        }
    }

    /**
     * 提交云剪辑工程导出任务
     * <p>
     * 支持将云剪辑工程导出为视频文件或Adobe PR工程文件。
     * 导出结果将保存到指定的OSS存储桶中。
     * </p>
     *
     * @param requestDTO 包含导出任务所需全部参数的数据传输对象
     * @return 返回包含导出任务JobId等信息的AjaxResult对象
     */
    @Operation(summary = "提交云剪辑工程导出任务")
    @PostMapping("/export")
    public AjaxResult submitProjectExportJob(@RequestBody SubmitProjectExportRequestDTO requestDTO) {
        if (requestDTO == null) {
            return AjaxResult.error("请求参数不能为空");
        }

        try {
            String result = videoProjectService.submitProjectExportJob(requestDTO);
            return AjaxResult.success("提交导出任务成功", JSON.parse(result));
        } catch (Exception e) {
            log.error("提交导出任务失败", e);
            return AjaxResult.error("提交导出任务失败: " + e.getMessage());
        }
    }

    /**
     * 查询云剪辑工程导出任务状态和结果
     * <p>
     * 用于查询通过导出接口提交的任务的执行状态、进度和结果。
     * 可以获取任务的详细信息，包括导出文件的下载地址（成功时）或错误信息（失败时）。
     * </p>
     *
     * @param jobId 工程导出任务ID，从URL路径中获取
     * @return 返回包含任务状态和结果信息的AjaxResult对象
     */
    @Operation(summary = "查询云剪辑工程导出任务状态和结果")
    @GetMapping("/export/{jobId}")
    public AjaxResult getProjectExportJob(@PathVariable String jobId) {
        if (jobId == null || jobId.trim().isEmpty()) {
            return AjaxResult.error("jobId 不能为空");
        }

        try {
            String result = videoProjectService.getProjectExportJob(jobId);
            return AjaxResult.success("查询导出任务成功", JSON.parse(result));
        } catch (Exception e) {
            log.error("查询导出任务失败", e);
            return AjaxResult.error("查询导出任务失败: " + e.getMessage());
        }
    }
}