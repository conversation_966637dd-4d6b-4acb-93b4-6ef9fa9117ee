package com.ruoyi.video.utils;

import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.regex.Pattern;

import org.springframework.web.multipart.MultipartFile;

import com.ruoyi.file.utils.FileOperateUtils;

/**
 * 媒资相关工具类
 *
 * <AUTHOR>
 * @date 2025-07-12
 */
public class MediaUtils {

    // 文件类型常量
    public static final String TYPE_VIDEO = "video";
    public static final String TYPE_AUDIO = "audio";
    public static final String TYPE_IMAGE = "image";
    public static final String TYPE_TEXT = "text";
    public static final String TYPE_OTHER = "other";

    // 日期格式常量
    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy/MM/dd");

    // 文件类型匹配模式
    private static final Pattern VIDEO_EXT_PATTERN = Pattern.compile("(?i)^(mp4|avi|mkv|mov|wmv|flv|webm|m4v)$");
    private static final Pattern AUDIO_EXT_PATTERN = Pattern.compile("(?i)^(mp3|wav|flac|aac|m4a|wma|ogg)$");
    private static final Pattern IMAGE_EXT_PATTERN = Pattern.compile("(?i)^(jpg|jpeg|png|gif|bmp|webp|svg)$");
    private static final Pattern TEXT_EXT_PATTERN = Pattern.compile("(?i)^(txt|srt|ass|vtt|sub)$");

    /**
     * 根据文件扩展名确定文件类型
     *
     * @param ext 文件扩展名
     * @return 文件类型：video、audio、image、text、other
     */
    public static String determineFileType(String ext) {
        if (ext == null || ext.isEmpty()) {
            return TYPE_OTHER;
        }

        String cleanExt = ext.toLowerCase().trim();

        if (VIDEO_EXT_PATTERN.matcher(cleanExt).matches()) {
            return TYPE_VIDEO;
        } else if (AUDIO_EXT_PATTERN.matcher(cleanExt).matches()) {
            return TYPE_AUDIO;
        } else if (IMAGE_EXT_PATTERN.matcher(cleanExt).matches()) {
            return TYPE_IMAGE;
        } else if (TEXT_EXT_PATTERN.matcher(cleanExt).matches()) {
            return TYPE_TEXT;
        } else {
            return TYPE_OTHER;
        }
    }

    /**
     * 构建框架分类路径
     *
     * 路径格式：{category}/{date}/{timestamp}_{fileName}
     * 例如：video/2025/07/12/1720780800000_example.mp4
     *
     * @param category 分类名称，如：video、audio、image等
     * @param fileName 原始文件名
     * @return 构建后的分类路径
     */
    public static String buildFrameworkCategoryPath(String category, String fileName) {
        if (fileName == null || fileName.isEmpty()) {
            throw new IllegalArgumentException("文件名不能为空");
        }

        String currentDate = LocalDate.now().format(DATE_FORMATTER);
        String timestamp = String.valueOf(System.currentTimeMillis());

        return String.format("%s/%s/%s_%s",
                sanitizeCategory(category),
                currentDate,
                timestamp,
                fileName);
    }

    /**
     * 构建完整的OSS URL
     *
     * OSS URL格式：oss://bucketName/filePath
     *
     * @param bucketName OSS桶名
     * @param filePath   文件路径
     * @return 完整的OSS URL
     */
    public static String buildOssUrl(String bucketName, String filePath) {
        if (bucketName == null || bucketName.isEmpty()) {
            throw new IllegalArgumentException("OSS桶名不能为空");
        }

        String cleanPath = sanitizeFilePath(filePath);
        return String.format("oss://%s/%s", bucketName, cleanPath);
    }

    /**
     * 从文件名中提取扩展名
     *
     * @param fileName 文件名
     * @return 扩展名（不包含点），如果没有扩展名则返回空字符串
     */
    public static String getFileExtension(String fileName) {
        if (fileName == null || fileName.isEmpty()) {
            return "";
        }

        int lastDotIndex = fileName.lastIndexOf('.');
        if (lastDotIndex == -1 || lastDotIndex == fileName.length() - 1) {
            return "";
        }

        return fileName.substring(lastDotIndex + 1);
    }

    /**
     * 验证文件扩展名是否为支持的媒资类型
     *
     * @param ext 文件扩展名
     * @return true表示支持，false表示不支持
     */
    public static boolean isSupportedMediaType(String ext) {
        return !TYPE_OTHER.equals(determineFileType(ext));
    }

    /**
     * 构建ICE媒资库的完整路径
     *
     * 标准格式：ice/MediaLibrary/{category}/{date}/{timestamp}_{fileName}
     *
     * @param category 媒资分类
     * @param fileName 文件名
     * @return ICE媒资库路径
     */
    public static String buildIceMediaLibraryPath(String category, String fileName) {
        String frameworkPath = buildFrameworkCategoryPath(category, fileName);
        return "ice/MediaLibrary/" + frameworkPath;
    }

    /**
     * 清理分类名称，确保其有效性
     */
    private static String sanitizeCategory(String category) {
        if (category == null || category.isEmpty()) {
            return TYPE_OTHER;
        }

        // 移除特殊字符，只保留字母、数字和下划线
        return category.replaceAll("[^a-zA-Z0-9_]", "_");
    }

    /**
     * 清理文件路径，确保格式正确
     */
    private static String sanitizeFilePath(String filePath) {
        if (filePath == null) {
            return "";
        }

        // 移除开头的斜杠
        String cleanPath = filePath.startsWith("/") ? filePath.substring(1) : filePath;

        // 规范化路径分隔符
        return cleanPath.replace('\\', '/');
    }

    /** 获取文件的全路径 (保存上传文件的地方) */
    public static String getFileFullPath(MultipartFile file) {
        String basePath = "ice/mediaInput";
        String currentDate = new SimpleDateFormat("yyyy/MM/dd").format(new Date());
        String timestamp = String.valueOf(System.currentTimeMillis());
        String shortTimestamp = timestamp.substring(Math.max(0, timestamp.length() - 6));
        shortTimestamp = String.format("%06d", Long.parseLong(shortTimestamp));
        String originalFilename = file.getOriginalFilename();
        String newFileName = shortTimestamp + "_" + originalFilename;
        String fullPath = String.format("%s/%s/%s", basePath, currentDate, newFileName);
        return fullPath;
    }

    /** 获取文件合成结果的路径 (保存合成结果) */
    public static String getFileOutputPath() {
        String basePath = "ice/mediaOutput";
        String currentDate = new SimpleDateFormat("yyyy/MM/dd").format(new Date());
        String timestamp = String.valueOf(System.currentTimeMillis());
        String fileName = timestamp + "_output.mp4";
        String fullPath = String.format("%s/%s/%s", basePath, currentDate, fileName);
        return "https://szb-pc.oss-cn-beijing.aliyuncs.com/" + fullPath;

    }

    /**
     * 移除URL中的签名参数，返回纯净的OSS URL
     *
     * @param url 带签名的URL
     * @return 纯净的OSS URL
     */
    public static String removeUrlSignature(String url) {
        if (url == null || !url.contains("?")) {
            return url;
        }
        // 移除查询参数部分
        String cleanUrl = url.substring(0, url.indexOf("?"));

        return cleanUrl;
    }

    /**
     * 将OSS完整URL转换为可访问的临时URL
     *
     * @param ossFullUrl OSS完整URL
     * @return 带签名的临时访问URL
     */
    public static String convertToAccessibleUrl(String ossFullUrl) {
        if (ossFullUrl == null || !ossFullUrl.contains("oss-cn-beijing.aliyuncs.com/")) {
            return ossFullUrl;
        }
        try {
            // 提取相对路径
            String[] parts = ossFullUrl.split("oss-cn-beijing\\.aliyuncs\\.com/");
            if (parts.length > 1) {
                String relativePath = parts[1];
                // 使用FileOperateUtils生成临时访问URL
                return FileOperateUtils.getURL(relativePath);
            }
        } catch (Exception e) {
            throw new RuntimeException("处理视频URL失败");
        }

        return ossFullUrl;
    }
}