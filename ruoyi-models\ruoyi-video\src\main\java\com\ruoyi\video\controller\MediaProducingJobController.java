package com.ruoyi.video.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartRequest;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.ruoyi.common.annotation.Anonymous;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.video.domain.MediaRequest;
import com.ruoyi.video.service.IMediaProducingService;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.parameters.RequestBody;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;

/**
 * 批量智能一键成片任务管理 前端控制器
 * <p>
 * 负责接收和处理与阿里云ICE批量智能一键成片相关的HTTP请求。
 * 提供RESTful API，用于提交任务、查询任务状态等操作。
 * </p>
 *
 * <AUTHOR>
 * @date 2025-07-12
 */
@Slf4j
@RestController
@RequestMapping("/media/mediaProducing")
@Tag(name = "【剪辑合成任务】")
public class MediaProducingJobController {

    @Autowired
    private IMediaProducingService mediaProducingService;

    /**
     * 提交剪辑合成作业
     */
    @Operation(summary = "提交剪辑合成作业")
    @PostMapping("/submit")
    public AjaxResult submitMediaProducingJob(
            MultipartRequest files,
            @RequestParam("templateId") String templateId,
            @RequestParam("clipsParam") String clipsParam) {
        try {
            MediaRequest request = new MediaRequest();
            request.setTemplateId(templateId);
            request.setClipsParam(clipsParam);
            String result = mediaProducingService.submitMediaProducingJob(files, request);
            return AjaxResult.success("提交成功", JSON.parse(result));
        } catch (Exception e) {
            return AjaxResult.error("提交失败: " + e.getMessage());
        }
    }

    /**
     * 获取单个合成任务详情
     */
    @Operation(summary = "获取单个合成任务详情")
    @GetMapping("/job/{jobId}")
    @Anonymous
    public AjaxResult getMediaProducingJobByJobId(@PathVariable String jobId) {
        try {
            String result = mediaProducingService.getMediaProducingJobByJobId(jobId);
            return AjaxResult.success("获取成功", JSON.parse(result));
        } catch (Exception e) {
            return AjaxResult.error("获取失败: " + e.getMessage());
        }
    }

    /**
     * 剪辑合成任务完成回调地址
     */
    @Anonymous
    @Operation(summary = "剪辑合成任务完成回调地址")
    @PostMapping("/callback")
    public AjaxResult receiveIceCallback(@RequestBody String callbackData) {
        try {
            JSONObject callbackJson = JSON.parseObject(callbackData);
            String eventType = callbackJson.getString("EventType");

            if (!"ProduceMediaComplete".equals(eventType))
                return AjaxResult.success("回调已忽略");

            JSONObject messageBody = callbackJson.getJSONObject("MessageBody");

            if (messageBody == null)
                return AjaxResult.error("回调消息体为空");

            String projectId = messageBody.getString("ProjectId");
            String jobId = messageBody.getString("JobId");
            String status = messageBody.getString("Status");
            String mediaId = messageBody.getString("MediaId");
            String mediaURL = messageBody.getString("MediaURL");

            log.info("批量智能一键成片任务完成 - ProjectId: {}, JobId: {}, Status: {}, MediaId: {}, MediaURL: {}",
                    projectId, jobId, status, mediaId, mediaURL);
            return AjaxResult.success("回调处理成功");
        } catch (Exception e) {
            return AjaxResult.error("回调处理失败: " + e.getMessage());
        }
    }

}