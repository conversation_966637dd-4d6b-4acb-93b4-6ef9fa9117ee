# 基于现有剪辑服务的数字人对话视频合成集成指南

## 🎯 集成概述

我已经成功将你现有的剪辑服务集成到数字人对话合成业务中，现在你可以使用模板方式进行云剪辑合成。

## 🔧 技术实现

### 1. 核心集成方法

#### `createDialogueVideoClipByTemplate()`

- **功能**: 使用现有剪辑服务进行对话视频合成
- **参数**:
  - `dialogueGroupId`: 对话组ID
  - `templateId`: 模板ID
  - `title`: 视频标题（可选）
  - `description`: 视频描述（可选）

#### 实现原理

```java
// 1. 查找对话组的所有已完成任务
List<PlatformVideo> groupTasks = findTasksByDialogueGroupId(dialogueGroupId);

// 2. 构建clipsParam参数
JSONObject clipsParam = buildClipsParamForTemplate(groupTasks);
// 例如: {"media1": "mediaId", "media2": "mediaId"}

// 3. 调用阿里云ICE API
SubmitMediaProducingJobRequest request = new SubmitMediaProducingJobRequest();
request.setTemplateId(templateId);
request.setClipsParam(actualClipsParam.toJSONString());
```

### 2. 关键优化

#### A. 智能参数映射

- 自动将数字人视频文件映射到模板参数
- 支持多个视频文件的顺序映射
- 自动转换OSS URL格式

#### B. 无文件上传设计

- 直接使用已生成的数字人视频文件
- 避免重复上传，提高效率
- 自动清理URL签名参数

#### C. 完整的状态管理

- 任务状态验证
- 剪辑状态标记
- 详细的日志记录

## 🚀 使用方法

### 1. API接口

#### 主要接口

```http
POST /platform/video/dialogueVideoClipByTemplate
```

**参数**:

- `dialogueGroupId`: 对话组ID（必填）
- `templateId`: 模板ID（必填）
- `title`: 视频标题（可选）
- `description`: 视频描述（可选）

#### 测试接口

```http
POST /platform/video/testTemplateClip
```

**参数**:

- `dialogueGroupId`: 对话组ID（必填）
- `templateId`: 模板ID（可选，默认"test-template-id"）

### 2. 使用流程

#### 步骤1: 完成数字人对话合成

```http
POST /platform/video/dialogueSynthesis
```

#### 步骤2: 检查对话组状态

```http
POST /platform/video/checkDialogueGroupStatus?dialogueGroupId=dialogue_xxx
```

#### 步骤3: 执行模板剪辑

```http
POST /platform/video/dialogueVideoClipByTemplate
Content-Type: application/x-www-form-urlencoded

dialogueGroupId=dialogue_1753864491055&templateId=your-template-id&title=测试视频
```

### 3. 响应格式

#### 成功响应

```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "requestId": "xxx-xxx-xxx",
    "projectId": "xxx-project-id",
    "jobId": "xxx-job-id",
    "mediaId": "xxx-media-id",
    "vodMediaId": "xxx-vod-id",
    "outputPath": "https://szb-pc.oss-cn-beijing.aliyuncs.com/dialogue_videos/template_dialogue_video_xxx.mp4",
    "message": "模板剪辑任务提交成功"
  }
}
```

## 📊 技术优势

### 1. 兼容性

- ✅ 完全兼容现有剪辑服务
- ✅ 支持PC和移动端
- ✅ 保持原有API接口不变

### 2. 效率提升

- ✅ 无需重复上传视频文件
- ✅ 直接使用已生成的数字人视频
- ✅ 自动化参数映射

### 3. 可靠性

- ✅ 完整的错误处理
- ✅ 详细的日志记录
- ✅ 状态验证机制

## 🔍 测试建议

### 1. 基础测试

```bash
# 1. 先创建数字人对话
curl -X POST "http://localhost:8899/platform/video/dialogueSynthesis" \
  -H "Content-Type: application/json" \
  -d '{
    "digitalHumans": [
      {
        "speakerId": "human_1",
        "speakerName": "数字人1",
        "text": "测试对话内容1",
        "avatarAddress": "video/image/xxx.mp4",
        "voiceName": "aida",
        "voiceType": "builtin"
      },
      {
        "speakerId": "human_2", 
        "speakerName": "数字人2",
        "text": "测试对话内容2",
        "avatarAddress": "video/image/yyy.mp4",
        "voiceName": "xiaogang",
        "voiceType": "builtin"
      }
    ]
  }'

# 2. 等待任务完成后，执行模板剪辑
curl -X POST "http://localhost:8899/platform/video/testTemplateClip" \
  -d "dialogueGroupId=dialogue_xxx&templateId=your-template-id"
```

### 2. 状态检查

```bash
# 检查对话组状态
curl -X POST "http://localhost:8899/platform/video/checkDialogueGroupStatus?dialogueGroupId=dialogue_xxx"
```

## 🎉 集成完成

现在你可以：

1. ✅ 使用现有的剪辑服务
2. ✅ 支持模板化视频合成
3. ✅ 无需修改现有业务逻辑
4. ✅ 享受更高的合成成功率

你的数字人对话合成功能现在已经完全集成了现有的剪辑服务，可以开始测试使用了！
