# 智能模板选择系统使用指南

## 🎯 系统概述

我已经为你创建了一个智能的模板选择系统，前端只需要提供对话组ID，后端会自动选择最适合的模板进行剪辑。

## 🔧 后端智能配置

### 1. 预定义模板配置
```java
// 根据你的模板数据预定义的配置
private static final Map<String, String> DIALOGUE_TEMPLATES = new HashMap<String, String>() {{
    // 2人对话模板 - 支持3个媒体参数
    put("2_PERSON_DIALOGUE", "29bcaa98249349b79cdb693b21ba3860"); // 测试数据模板
    // 多人对话模板 - 支持3个媒体参数  
    put("MULTI_PERSON_DIALOGUE", "a8ca479ecb554b9baefd8040f4592127"); // 默认模板
    // 备用模板
    put("FALLBACK_TEMPLATE", "29bcaa98249349b79cdb693b21ba3860"); // 测试数据模板
}};
```

### 2. 智能选择逻辑
- **2人对话**: 自动选择 `29bcaa98249349b79cdb693b21ba3860` (测试数据模板)
- **3人及以上**: 自动选择 `a8ca479ecb554b9baefd8040f4592127` (默认模板)
- **备用方案**: 如果首选模板不可用，自动回退到备用模板

### 3. 参数映射规则
```java
// 测试数据模板 (29bcaa98249349b79cdb693b21ba3860)
"1cc37b" -> 第1个视频
"823b59" -> 第2个视频  
"e5f45b" -> 第3个视频

// 默认模板 (a8ca479ecb554b9baefd8040f4592127)
"4d9f4b" -> 第1个视频
"c6ce51" -> 第2个视频
"c1d795" -> 第3个视频
```

## 🚀 前端调用方式

### 1. 一键剪辑接口（推荐）
```javascript
// 最简单的调用方式，后端自动选择最佳模板
const response = await fetch('/platform/video/autoDialogueClip', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/x-www-form-urlencoded',
  },
  body: new URLSearchParams({
    dialogueGroupId: 'dialogue_1753927830739',
    title: '我的对话视频',
    description: '这是一个自动剪辑的对话视频'
  })
});
```

### 2. 智能剪辑接口
```javascript
// 可以指定模板，也可以让系统自动选择
const response = await fetch('/platform/video/smartDialogueClip', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/x-www-form-urlencoded',
  },
  body: new URLSearchParams({
    dialogueGroupId: 'dialogue_1753927830739',
    templateId: '', // 留空让系统自动选择
    title: '我的对话视频'
  })
});
```

### 3. Vue.js 完整示例
```vue
<template>
  <div class="dialogue-clip">
    <el-form @submit.prevent="submitClip">
      <el-form-item label="对话组ID">
        <el-input v-model="dialogueGroupId" placeholder="例如: dialogue_1753927830739" />
      </el-form-item>
      
      <el-form-item label="视频标题">
        <el-input v-model="title" placeholder="可选" />
      </el-form-item>
      
      <el-form-item label="视频描述">
        <el-input v-model="description" type="textarea" placeholder="可选" />
      </el-form-item>
      
      <el-form-item>
        <el-button type="primary" @click="submitClip" :loading="loading">
          一键剪辑
        </el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
export default {
  data() {
    return {
      dialogueGroupId: '',
      title: '',
      description: '',
      loading: false
    }
  },
  methods: {
    async submitClip() {
      if (!this.dialogueGroupId) {
        this.$message.error('对话组ID不能为空');
        return;
      }

      this.loading = true;
      try {
        const response = await this.$http.post('/platform/video/autoDialogueClip', {
          dialogueGroupId: this.dialogueGroupId,
          title: this.title,
          description: this.description
        });

        if (response.data.code === 200) {
          this.$message.success('剪辑任务提交成功！');
          console.log('任务信息:', response.data.data);
          
          // 可以跳转到任务状态页面或显示进度
          this.showTaskProgress(response.data.data.jobId);
        } else {
          this.$message.error(response.data.msg);
        }
      } catch (error) {
        this.$message.error('提交失败: ' + error.message);
      } finally {
        this.loading = false;
      }
    },

    showTaskProgress(jobId) {
      // 显示任务进度或跳转到状态页面
      this.$router.push(`/task-status/${jobId}`);
    }
  }
}
</script>
```

## 📊 API接口总览

### 1. 一键剪辑（推荐）
```http
POST /platform/video/autoDialogueClip
```
**参数**:
- `dialogueGroupId`: 对话组ID（必填）
- `title`: 视频标题（可选）
- `description`: 视频描述（可选）

**特点**: 完全自动化，后端智能选择最佳模板

### 2. 智能剪辑
```http
POST /platform/video/smartDialogueClip
```
**参数**:
- `dialogueGroupId`: 对话组ID（必填）
- `templateId`: 模板ID（可选，留空自动选择）
- `title`: 视频标题（可选）
- `description`: 视频描述（可选）

**特点**: 可以指定模板，也可以自动选择

### 3. 指定模板剪辑
```http
POST /platform/video/dialogueVideoClipByTemplate
```
**参数**:
- `dialogueGroupId`: 对话组ID（必填）
- `templateId`: 模板ID（必填）
- `title`: 视频标题（可选）
- `description`: 视频描述（可选）

**特点**: 必须指定具体的模板ID

## 🎉 优势总结

### 1. 前端简化
- ✅ 无需处理复杂的模板选择逻辑
- ✅ 无需了解模板参数映射
- ✅ 只需提供对话组ID即可

### 2. 后端智能
- ✅ 自动根据视频数量选择最佳模板
- ✅ 自动处理参数映射
- ✅ 自动验证模板可用性
- ✅ 智能回退机制

### 3. 用户体验
- ✅ 一键操作，简单易用
- ✅ 自动选择最佳效果
- ✅ 容错性强，稳定可靠

## 🔍 测试建议

```bash
# 测试一键剪辑
curl -X POST "http://localhost:8899/platform/video/autoDialogueClip" \
  -d "dialogueGroupId=dialogue_1753927830739&title=测试视频"

# 查看任务状态
curl -X GET "http://localhost:8899/platform/video/checkDialogueGroupStatus?dialogueGroupId=dialogue_1753927830739"
```

现在你的前端只需要调用 `/autoDialogueClip` 接口，后端会自动处理所有复杂的模板选择和参数映射逻辑！🎉
